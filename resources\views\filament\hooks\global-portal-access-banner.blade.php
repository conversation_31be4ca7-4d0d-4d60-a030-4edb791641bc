@php

use App\Enums\AdmissionStatus;
use App\Enums\Role;
use App\Filament\Student\Clusters\Cpay\Resources\PortalResource;
use App\Enums\InvoiceStatus;
use Illuminate\Support\Facades\Auth;

$user = Auth::user();

$hasUnpaidPortalFee = $user->registrations()
        ->whereDoesntHave('portalInvoice', function ($query) {
            $query->where('invoice_status', InvoiceStatus::PAID);
        })
        ->exists();

@endphp

@if($user->role === Role::STUDENT && $user->application?->admission_status === AdmissionStatus::APPROVED && $hasUnpaidPortalFee)
<div class="bg-amber-100 border border-amber-400 text-amber-800 px-4 py-2 rounded flex items-center justify-center gap-2 mt-4 text-sm font-semibold">
    <x-heroicon-s-exclamation-circle class="w-5 h-5 text-amber-600" />
    You have unpaid portal fees. Please pay now to get full access.
    <a href="{{ PortalResource::getUrl('index') }}" class="text-blue-600 underline">View portal fees</a>
</div>
@endif



