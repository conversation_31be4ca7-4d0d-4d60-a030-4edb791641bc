@php
use App\Filament\Student\Pages\Auth\StudentProfile;
use App\Filament\Staff\Pages\Auth\StaffProfile;
use Illuminate\Support\Facades\Auth;
use App\Enums\Role;

$user = Auth::user();

$hasIncompleteStudentBioData =
   !$user->address_line || !$user->address_town || !$user->address_state ||
   !$user->date_of_birth || !$user->gender || !$user->marital_status || !$user->religion || !$user->nationality ||
   !$user->state_id || !$user->local_government_area_id ||

   !$user->application || !$user->application?->programme_id || !$user->application?->school_session_id ||
   !$user->application?->secondary_school_attended || !$user->application?->secondary_school_graduation_year ||
   !$user->application?->exam_board || !$user->application?->exam_result ||
   
   !$user->guardian || !$user->guardian?->relationship || !$user->guardian?->occupation || !$user->guardian?->phone ||
   !$user->guardian?->first_name || !$user->guardian?->last_name || !$user->guardian?->title;

$hasIncompleteStaffBioData = 
   !$user->address_line || !$user->address_town || !$user->address_state ||
   !$user->date_of_birth || !$user->gender || !$user->marital_status || !$user->religion;

$isStudent = $user->role === Role::STUDENT;
$showWarning = ($isStudent && $hasIncompleteStudentBioData) || (!$isStudent && $hasIncompleteStaffBioData);
$profileUrl = $isStudent ? StudentProfile::getUrl(['panel' => 'student']) : StaffProfile::getUrl(['panel' => 'staff']);
@endphp

@if($showWarning)
<div class="bg-amber-100 border border-amber-400 text-amber-800 px-4 py-2 rounded flex items-center justify-center gap-2 mt-4 text-sm font-semibold">
    <x-heroicon-s-exclamation-circle class="w-5 h-5 text-amber-600"/>
    Your bio-data is incomplete. Please update your details.
    <a href="{{ $profileUrl }}" class="text-blue-600 underline">Bio-data page</a>
</div>
@endif