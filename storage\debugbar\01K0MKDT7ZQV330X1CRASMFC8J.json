{"__meta": {"id": "01K0MKDT7ZQV330X1CRASMFC8J", "datetime": "2025-07-20 19:02:01", "utime": **********.215495, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.063001, "end": **********.215527, "duration": 2.1525261402130127, "duration_str": "2.15s", "measures": [{"label": "Booting", "start": **********.063001, "relative_start": 0, "end": **********.479536, "relative_end": **********.479536, "duration": 0.****************, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.479555, "relative_start": 0.*****************, "end": **********.21553, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.74s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.483471, "relative_start": 0.****************, "end": **********.483978, "relative_end": **********.483978, "duration": 0.0005071163177490234, "duration_str": "507μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.137916, "relative_start": 2.****************, "end": **********.137916, "relative_end": **********.137916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.150089, "relative_start": 2.***************, "end": **********.150089, "relative_end": **********.150089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.158617, "relative_start": 2.095616102218628, "end": **********.158617, "relative_end": **********.158617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.169398, "relative_start": 2.1063971519470215, "end": **********.169398, "relative_end": **********.169398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.175802, "relative_start": 2.1128010749816895, "end": **********.175802, "relative_end": **********.175802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.201715, "relative_start": 2.138714075088501, "end": **********.201715, "relative_end": **********.201715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.20514, "relative_start": 2.142139196395874, "end": **********.20514, "relative_end": **********.20514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3e7af8a9f04a41a7583e36fe677e9b1b", "start": **********.208106, "relative_start": 2.1451051235198975, "end": **********.208106, "relative_end": **********.208106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.214426, "relative_start": 2.1514251232147217, "end": **********.215121, "relative_end": **********.215121, "duration": 0.0006949901580810547, "duration_str": "695μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8406600, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.13784, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.150021, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.158544, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.16933, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.175693, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.201658, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.20502, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::3e7af8a9f04a41a7583e36fe677e9b1b", "param_count": null, "params": [], "start": **********.207994, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/3e7af8a9f04a41a7583e36fe677e9b1b.blade.php__components::3e7af8a9f04a41a7583e36fe677e9b1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F3e7af8a9f04a41a7583e36fe677e9b1b.blade.php&line=1", "ajax": false, "filename": "3e7af8a9f04a41a7583e36fe677e9b1b.blade.php", "line": "?"}}]}, "queries": {"count": 455, "nb_statements": 454, "nb_visible_statements": 455, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.6889600000000002, "accumulated_duration_str": "689ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 354 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.488622, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'aYGnG8gbrucmIdT9M2DJDYTxkt3PKUtjFTE7Fv4h' limit 1", "type": "query", "params": [], "bindings": ["aYGnG8gbrucmIdT9M2DJDYTxkt3PKUtjFTE7Fv4h"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.489519, "duration": 0.0061600000000000005, "duration_str": "6.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0.894}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.502568, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 0.894, "width_percent": 0.122}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.520072, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 1.016, "width_percent": 0.096}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.523922, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 1.112, "width_percent": 0.087}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-07-20' and date(`semester_end`) >= '2025-07-20' limit 1", "type": "query", "params": [], "bindings": [3, "2025-07-20", "2025-07-20"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.527612, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 1.199, "width_percent": 0.152}, {"sql": "select * from `semesters` where `semesters`.`id` in (2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.5323, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 1.351, "width_percent": 0.237}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.5376668, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 1.588, "width_percent": 0.29}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.543183, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 1.878, "width_percent": 0.177}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.548704, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 2.055, "width_percent": 0.226}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.5549731, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 2.282, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.560021, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 2.478, "width_percent": 0.158}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.566561, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 2.636, "width_percent": 0.216}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.571698, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 2.852, "width_percent": 0.173}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.576473, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 3.025, "width_percent": 0.222}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.584056, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 3.247, "width_percent": 0.367}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.590264, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 3.614, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.595941, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 3.794, "width_percent": 0.209}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.603489, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 4.003, "width_percent": 0.208}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.6083312, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 4.211, "width_percent": 0.192}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.613674, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 4.402, "width_percent": 0.203}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.62111, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 4.605, "width_percent": 0.253}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.628527, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 4.858, "width_percent": 0.231}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.636163, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 5.089, "width_percent": 0.179}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.641157, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 5.267, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.646435, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 5.447, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.654526, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 5.633, "width_percent": 0.19}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.659824, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 5.823, "width_percent": 0.183}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.665211, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 6.006, "width_percent": 0.337}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.671041, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 6.343, "width_percent": 0.193}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.6761699, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 6.536, "width_percent": 0.16}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.68121, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 6.696, "width_percent": 0.247}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.689124, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 6.942, "width_percent": 0.205}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.69466, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 7.147, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.701649, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 7.327, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.706706, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 7.514, "width_percent": 0.2}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.711723, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 7.715, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.7178051, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 7.89, "width_percent": 0.422}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.724771, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 8.313, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.730577, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 8.493, "width_percent": 0.165}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.736706, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 8.658, "width_percent": 0.132}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.7416, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 8.79, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.75183, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 8.957, "width_percent": 0.406}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.760561, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 9.363, "width_percent": 0.205}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.768137, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 9.568, "width_percent": 0.232}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.774131, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 9.8, "width_percent": 0.174}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.7793632, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 9.974, "width_percent": 0.174}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.7874942, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 10.149, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.793094, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 10.368, "width_percent": 0.206}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.799588, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 10.574, "width_percent": 0.414}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.809942, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 10.988, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.81576, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 11.184, "width_percent": 0.491}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.827069, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 11.674, "width_percent": 0.203}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.835433, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 11.877, "width_percent": 0.197}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.841756, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 12.075, "width_percent": 0.205}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.847644, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 12.279, "width_percent": 0.192}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.8558319, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 12.471, "width_percent": 0.206}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.8615148, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 12.677, "width_percent": 0.212}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.869554, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 12.889, "width_percent": 0.2}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.874976, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 13.089, "width_percent": 0.189}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.8808382, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 13.278, "width_percent": 0.221}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.889242, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 13.499, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.8947659, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 13.679, "width_percent": 0.216}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.9022489, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 13.895, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.9079711, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 14.082, "width_percent": 0.168}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.913852, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 14.25, "width_percent": 0.222}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.923383, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 14.473, "width_percent": 0.224}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": **********.938872, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 14.696, "width_percent": 0.197}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.945728, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 14.893, "width_percent": 0.171}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": **********.951316, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 15.065, "width_percent": 0.406}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.9585311, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 15.471, "width_percent": 0.173}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": **********.963001, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 15.644, "width_percent": 0.194}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.9692318, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 15.838, "width_percent": 0.19}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": **********.974962, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 16.029, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.980197, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 16.198, "width_percent": 0.189}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": **********.986753, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 16.387, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": **********.991759, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 16.563, "width_percent": 0.154}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": **********.996648, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 16.717, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.0035, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 16.912, "width_percent": 0.221}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.008764, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 17.133, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.013748, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 17.315, "width_percent": 0.152}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.0204291, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 17.467, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.026984, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 17.663, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.034109, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 17.882, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.040638, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 18.101, "width_percent": 0.21}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.0468469, "duration": 0.00266, "duration_str": "2.66ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 18.312, "width_percent": 0.386}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.0562031, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 18.698, "width_percent": 0.253}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.061913, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 18.95, "width_percent": 0.218}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.068393, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 19.168, "width_percent": 0.184}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.074537, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 19.352, "width_percent": 0.237}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.080736, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 19.589, "width_percent": 0.239}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.088607, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 19.828, "width_percent": 0.248}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.094771, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 20.077, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.101236, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 20.262, "width_percent": 0.205}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 309}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.106353, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 20.467, "width_percent": 0.2}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.1110148, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 20.667, "width_percent": 0.161}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 333}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.116397, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 20.828, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.123494, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 21.016, "width_percent": 0.131}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 357}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.127619, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 21.146, "width_percent": 0.173}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 78}], "start": ********20.1321511, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 21.319, "width_percent": 0.324}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = '16' and `level_id` = '1' and `semester_id` = '1' order by `code` asc", "type": "query", "params": [], "bindings": ["16", "1", "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 285}, {"index": 17, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeHidden.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeHidden.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Columns/Concerns/CanBeToggled.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Columns\\Concerns\\CanBeToggled.php", "line": 35}], "start": ********20.137502, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:232", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 232}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=232", "ajax": false, "filename": "OverviewResource.php", "line": "232"}, "connection": "racoed", "explain": null, "start_percent": 21.643, "width_percent": 0.151}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.1417642, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 21.794, "width_percent": 0.142}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.14366, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 21.936, "width_percent": 0.158}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.145641, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 22.094, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.147671, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 22.261, "width_percent": 0.21}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.150136, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 22.472, "width_percent": 0.398}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.153772, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 22.869, "width_percent": 0.168}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.1559062, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 23.038, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.15804, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 23.205, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.160048, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 23.371, "width_percent": 0.157}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.162143, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 23.528, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.1646109, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 23.71, "width_percent": 0.309}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.167789, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 24.019, "width_percent": 0.15}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.169765, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 24.168, "width_percent": 0.165}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.1718402, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 24.334, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.17384, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 24.501, "width_percent": 0.174}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.175962, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 24.675, "width_percent": 0.157}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.177782, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 24.832, "width_percent": 0.148}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.179563, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 24.98, "width_percent": 0.141}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.181396, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 25.12, "width_percent": 0.254}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.184523, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 25.374, "width_percent": 0.383}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.188122, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 25.758, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.190357, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 25.939, "width_percent": 0.171}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.192473, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.11, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.194828, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.296, "width_percent": 0.179}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.197837, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.475, "width_percent": 0.324}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.201054, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.798, "width_percent": 0.141}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.202891, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 26.939, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2050478, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.109, "width_percent": 0.16}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.207087, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.269, "width_percent": 0.173}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.209274, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.441, "width_percent": 0.173}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2113018, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.614, "width_percent": 0.164}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2133372, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.778, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.215822, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 27.945, "width_percent": 0.21}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.219416, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.155, "width_percent": 0.232}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.222046, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.388, "width_percent": 0.177}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.22424, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.565, "width_percent": 0.2}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.226882, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.765, "width_percent": 0.174}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.228968, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 28.939, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2316868, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.121, "width_percent": 0.189}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.234152, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.309, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.236643, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.485, "width_percent": 0.183}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.238989, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.668, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.241252, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 29.844, "width_percent": 0.164}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.243363, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.008, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2455401, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.189, "width_percent": 0.193}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.248122, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.382, "width_percent": 0.325}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.251946, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 30.707, "width_percent": 0.302}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.255284, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.009, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.257576, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.19, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2597978, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.378, "width_percent": 0.19}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.262036, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.568, "width_percent": 0.192}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.265029, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.759, "width_percent": 0.222}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.267713, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 31.982, "width_percent": 0.193}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2700589, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.175, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.272314, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.355, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2746549, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.551, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2771251, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.738, "width_percent": 0.218}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2796538, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 32.955, "width_percent": 0.189}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.282473, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.144, "width_percent": 0.2}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.2853088, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.344, "width_percent": 0.364}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.288767, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.709, "width_percent": 0.193}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.291352, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 33.902, "width_percent": 0.179}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.293681, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.08, "width_percent": 0.184}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.296123, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.265, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.298531, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.435, "width_percent": 0.286}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.3017519, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.72, "width_percent": 0.21}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.304726, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 34.931, "width_percent": 0.21}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.307439, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.141, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.309797, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.321, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.312418, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.541, "width_percent": 0.224}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.3152719, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 35.764, "width_percent": 0.316}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.319165, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.08, "width_percent": 0.321}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.322367, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.401, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.324564, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.583, "width_percent": 0.173}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.326706, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.755, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.3288941, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 36.935, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.331175, "duration": 0.00296, "duration_str": "2.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.105, "width_percent": 0.43}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.335175, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.535, "width_percent": 0.206}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.337777, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.741, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.340225, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 37.927, "width_percent": 0.168}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.342438, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.095, "width_percent": 0.177}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.344583, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.272, "width_percent": 0.144}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.346556, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.416, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.349067, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.596, "width_percent": 0.26}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.352354, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 38.856, "width_percent": 0.409}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.356142, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.265, "width_percent": 0.226}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.3589811, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.491, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.361152, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.661, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.364241, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 39.843, "width_percent": 0.184}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.367333, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.027, "width_percent": 0.152}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.369535, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.179, "width_percent": 0.199}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.372025, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.378, "width_percent": 0.164}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.374149, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.542, "width_percent": 0.179}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.376395, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.721, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.378762, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 40.907, "width_percent": 0.192}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.381036, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.098, "width_percent": 0.226}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.383905, "duration": 0.00297, "duration_str": "2.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.325, "width_percent": 0.431}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.388012, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.756, "width_percent": 0.202}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.390547, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 41.957, "width_percent": 0.199}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.392964, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 42.156, "width_percent": 0.213}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.395611, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 42.37, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.3978992, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 42.545, "width_percent": 0.476}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.402121, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.021, "width_percent": 0.155}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.404104, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.177, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.406456, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.352, "width_percent": 0.193}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.4090018, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.545, "width_percent": 0.267}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.411902, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.812, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.414139, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 43.992, "width_percent": 0.208}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.41685, "duration": 0.0029, "duration_str": "2.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.2, "width_percent": 0.421}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.420908, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.621, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.4234002, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.807, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.425646, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 44.974, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.427789, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.141, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.430196, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.326, "width_percent": 0.193}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.432846, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.519, "width_percent": 0.276}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.435818, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.795, "width_percent": 0.19}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.438125, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 45.985, "width_percent": 0.183}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.440588, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.168, "width_percent": 0.152}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.442563, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.321, "width_percent": 0.174}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.444707, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.495, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.446902, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.67, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.450096, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 46.837, "width_percent": 0.266}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = ? and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.454006, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.103, "width_percent": 0.102}, {"sql": "select * from `semesters` where `semesters`.`id` = ? and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.4558651, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.204, "width_percent": 0.096}, {"sql": "select * from `levels` where `levels`.`id` = ? and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.457974, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.3, "width_percent": 0.099}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.459999, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.399, "width_percent": 0.091}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.461751, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.49, "width_percent": 0.177}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.464657, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.667, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.4675858, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 47.837, "width_percent": 0.19}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.470426, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.027, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.472639, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.209, "width_percent": 0.173}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.474741, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.382, "width_percent": 0.173}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.4769719, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.554, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.4792829, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.742, "width_percent": 0.165}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.4813762, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 48.907, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.4842389, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.126, "width_percent": 0.414}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.488198, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.54, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.490509, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.736, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.492953, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 49.923, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.495358, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.099, "width_percent": 0.491}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.500849, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.589, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.503118, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.777, "width_percent": 0.144}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.504993, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 50.92, "width_percent": 0.187}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.50728, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.107, "width_percent": 0.218}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.509709, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.325, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.512048, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.505, "width_percent": 0.179}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.514448, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.684, "width_percent": 0.302}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5184128, "duration": 0.0027, "duration_str": "2.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 51.986, "width_percent": 0.392}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.522243, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 52.377, "width_percent": 0.218}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.524836, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 52.595, "width_percent": 0.205}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5273461, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 52.8, "width_percent": 0.202}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.529692, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.002, "width_percent": 0.202}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5321991, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.203, "width_percent": 0.328}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.535424, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.531, "width_percent": 0.205}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.538022, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.736, "width_percent": 0.155}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.540302, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 53.891, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.542844, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.111, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5458798, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.33, "width_percent": 0.237}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.548898, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.566, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.55287, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.762, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.555082, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 54.958, "width_percent": 0.142}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5574012, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.1, "width_percent": 0.194}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.560051, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.295, "width_percent": 0.257}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.562993, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.552, "width_percent": 0.2}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5656362, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 55.752, "width_percent": 0.321}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5686939, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.073, "width_percent": 0.155}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5708232, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.228, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.573625, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.447, "width_percent": 0.171}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.5762992, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.619, "width_percent": 0.167}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.57864, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.786, "width_percent": 0.209}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.581229, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 56.995, "width_percent": 0.225}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.584225, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.22, "width_percent": 0.453}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.588526, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.672, "width_percent": 0.253}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.591397, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 57.925, "width_percent": 0.128}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.593321, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.053, "width_percent": 0.177}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.595546, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.23, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.598027, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.411, "width_percent": 0.298}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.601023, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.709, "width_percent": 0.171}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.603922, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 58.88, "width_percent": 0.306}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.607028, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.186, "width_percent": 0.189}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.609297, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.375, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.6116252, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.545, "width_percent": 0.168}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.614148, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.713, "width_percent": 0.202}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.617391, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 59.915, "width_percent": 0.356}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.621294, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.271, "width_percent": 0.183}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.623751, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.453, "width_percent": 0.251}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.626779, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.705, "width_percent": 0.171}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.629019, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 60.876, "width_percent": 0.226}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.6320791, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.102, "width_percent": 0.373}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.636252, "duration": 0.00267, "duration_str": "2.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.475, "width_percent": 0.388}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.6401281, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 61.863, "width_percent": 0.196}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.642617, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.059, "width_percent": 0.209}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.645229, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.268, "width_percent": 0.168}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.648166, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.436, "width_percent": 0.263}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.651616, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.699, "width_percent": 0.296}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.6551042, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 62.995, "width_percent": 0.269}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.658312, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.263, "width_percent": 0.245}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.661042, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.509, "width_percent": 0.216}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.6637108, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.725, "width_percent": 0.209}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.667381, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 63.934, "width_percent": 0.208}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.669987, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.142, "width_percent": 0.184}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.672185, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.326, "width_percent": 0.131}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.673904, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.457, "width_percent": 0.161}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.675942, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.618, "width_percent": 0.179}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.6785548, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.796, "width_percent": 0.19}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.680892, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 64.986, "width_percent": 0.221}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.6835032, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.207, "width_percent": 0.374}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.687067, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.581, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.689265, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.757, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.691456, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 65.943, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.693547, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.113, "width_percent": 0.163}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.695859, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.275, "width_percent": 0.202}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.698755, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.477, "width_percent": 0.293}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.701692, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.77, "width_percent": 0.125}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.703493, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 66.895, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.70748, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.065, "width_percent": 0.176}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.709702, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.24, "width_percent": 0.179}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.712027, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.419, "width_percent": 0.244}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.714786, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.663, "width_percent": 0.225}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.718511, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 67.888, "width_percent": 0.351}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.72243, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.239, "width_percent": 0.199}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.724739, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.438, "width_percent": 0.174}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.729694, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.612, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.733615, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 68.798, "width_percent": 0.254}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.736302, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.052, "width_percent": 0.235}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.739475, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.287, "width_percent": 0.225}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.742705, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.512, "width_percent": 0.251}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.745886, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 69.763, "width_percent": 0.255}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.7492208, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.019, "width_percent": 0.315}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.754217, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.334, "width_percent": 0.255}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.757146, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.589, "width_percent": 0.266}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.76017, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 70.855, "width_percent": 0.174}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.762318, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.029, "width_percent": 0.181}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.7646608, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.21, "width_percent": 0.369}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.7685359, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.579, "width_percent": 0.267}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.7716818, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 71.846, "width_percent": 0.241}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.7747128, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.087, "width_percent": 0.241}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.777567, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.328, "width_percent": 0.261}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.780388, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.589, "width_percent": 0.242}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.783737, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 72.832, "width_percent": 0.408}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.7877998, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.239, "width_percent": 0.216}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.79053, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.456, "width_percent": 0.206}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.793254, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.662, "width_percent": 0.263}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.796413, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 73.924, "width_percent": 0.258}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.799814, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.183, "width_percent": 0.315}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.803395, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.498, "width_percent": 0.225}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.806429, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.723, "width_percent": 0.234}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.8096912, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 74.956, "width_percent": 0.218}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.812387, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.174, "width_percent": 0.164}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.814546, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.338, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.817596, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 75.557, "width_percent": 0.443}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.821638, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76, "width_percent": 0.197}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.8239899, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.197, "width_percent": 0.183}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.8264902, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.38, "width_percent": 0.27}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.829896, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.65, "width_percent": 0.26}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.833805, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 76.91, "width_percent": 0.296}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.836831, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.206, "width_percent": 0.25}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.839952, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.456, "width_percent": 0.273}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.843339, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 77.729, "width_percent": 0.274}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.846951, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.003, "width_percent": 0.283}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.8502831, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.286, "width_percent": 0.502}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.855142, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 78.788, "width_percent": 0.229}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.857734, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.018, "width_percent": 0.216}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.860276, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.234, "width_percent": 0.213}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.862782, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.447, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.865024, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.617, "width_percent": 0.308}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.868293, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 79.925, "width_percent": 0.255}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.87139, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.18, "width_percent": 0.25}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.874475, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.43, "width_percent": 0.266}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.877637, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.696, "width_percent": 0.263}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.880635, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 80.958, "width_percent": 0.27}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.884048, "duration": 0.00313, "duration_str": "3.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.228, "width_percent": 0.454}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.888488, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.683, "width_percent": 0.261}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.891603, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 81.944, "width_percent": 0.266}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.89481, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.209, "width_percent": 0.264}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.898088, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.474, "width_percent": 0.338}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.901769, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 82.812, "width_percent": 0.255}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.9048529, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.067, "width_percent": 0.237}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.90766, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.304, "width_percent": 0.197}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.909854, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.501, "width_percent": 0.154}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.911643, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.655, "width_percent": 0.177}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.913877, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 83.832, "width_percent": 0.193}, {"sql": "select exists(select * from `score_sheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.917463, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.025, "width_percent": 0.26}, {"sql": "select * from `departments` where `departments`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.9203858, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.285, "width_percent": 0.135}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.92452, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.42, "width_percent": 0.276}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.927819, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.696, "width_percent": 0.244}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.931312, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 84.94, "width_percent": 0.329}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.934841, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.269, "width_percent": 0.231}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.93763, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.5, "width_percent": 0.208}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.940449, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.707, "width_percent": 0.238}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.943722, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 85.945, "width_percent": 0.232}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.947007, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.178, "width_percent": 0.231}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.95041, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.408, "width_percent": 0.263}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.95426, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.671, "width_percent": 0.197}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.9565651, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 86.869, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.958953, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.038, "width_percent": 0.184}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.961638, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.223, "width_percent": 0.221}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.96491, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.443, "width_percent": 0.226}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.967802, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.67, "width_percent": 0.242}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.971126, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 87.912, "width_percent": 0.229}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.974399, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.142, "width_percent": 0.232}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.977691, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.374, "width_percent": 0.238}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.980901, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.612, "width_percent": 0.26}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.984097, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 88.872, "width_percent": 0.46}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.9887319, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.332, "width_percent": 0.21}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.9915361, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.542, "width_percent": 0.244}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.994602, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 89.786, "width_percent": 0.258}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": ********20.998481, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.044, "width_percent": 0.226}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.001263, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.271, "width_percent": 0.163}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.00372, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.433, "width_percent": 0.231}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0063, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.664, "width_percent": 0.189}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.008792, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 90.853, "width_percent": 0.226}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.011654, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.079, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0140991, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.265, "width_percent": 0.168}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.017035, "duration": 0.0022400000000000002, "duration_str": "2.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.433, "width_percent": 0.325}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0206301, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.759, "width_percent": 0.218}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.023989, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 91.976, "width_percent": 0.253}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.027288, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.229, "width_percent": 0.282}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.030953, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.51, "width_percent": 0.284}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.034359, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.795, "width_percent": 0.189}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.036452, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 92.984, "width_percent": 0.141}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0384839, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.124, "width_percent": 0.186}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.04081, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.31, "width_percent": 0.245}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.043682, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.556, "width_percent": 0.17}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.046132, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.725, "width_percent": 0.192}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.048769, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 93.917, "width_percent": 0.18}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.051506, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.097, "width_percent": 0.428}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.055952, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.525, "width_percent": 0.283}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0595589, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 94.808, "width_percent": 0.264}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.063076, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.072, "width_percent": 0.254}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.066599, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.326, "width_percent": 0.286}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0697012, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.612, "width_percent": 0.219}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.072333, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 95.831, "width_percent": 0.193}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0750182, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.024, "width_percent": 0.244}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.078165, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.268, "width_percent": 0.244}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.081405, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.512, "width_percent": 0.255}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.085416, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 96.768, "width_percent": 0.428}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.08989, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.196, "width_percent": 0.261}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.093446, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.457, "width_percent": 0.266}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.097092, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 97.723, "width_percent": 0.287}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.101403, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.01, "width_percent": 0.245}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.104778, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.255, "width_percent": 0.261}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.107855, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.517, "width_percent": 0.202}, {"sql": "select `id`, `code`, `title`, `credit`, `course_status` from `courses` where `department_id` = ? and `level_id` = ? and `semester_id` = ? order by `code` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.11034, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.718, "width_percent": 0.196}, {"sql": "select count(*) as aggregate from `users` where `role` = ? and 1 = 0 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = ? and `semester_id` = ? and `level_id` = ? and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = ? or `second_department_id` = ?))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1129758, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 98.914, "width_percent": 0.138}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.131955, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.052, "width_percent": 0.134}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1458318, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.186, "width_percent": 0.089}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.154656, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.274, "width_percent": 0.11}, {"sql": "select `name`, `id` from `departments` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.164912, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.385, "width_percent": 0.109}, {"sql": "select exists(select * from `score_sheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.189967, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.493, "width_percent": 0.086}, {"sql": "select exists(select * from `score_sheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.191249, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.579, "width_percent": 0.087}, {"sql": "select exists(select * from `score_sheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.192627, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.666, "width_percent": 0.089}, {"sql": "select exists(select * from `score_sheets` where (`school_session_id` = ? and `semester_id` = ? and `department_id` = ?)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.193861, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.755, "width_percent": 0.08}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = ? and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = ? and `invoice_status` = ? and `fee_type` = ? and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.203363, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "racoed", "explain": null, "start_percent": 99.835, "width_percent": 0.165}]}, "models": {"data": {"App\\Models\\Course": {"value": 3888, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FCourse.php&line=1", "ajax": false, "filename": "Course.php", "line": "?"}}, "App\\Models\\Department": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\Semester": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\SemesterSchedule": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemesterSchedule.php&line=1", "ajax": false, "filename": "SemesterSchedule.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}}, "count": 3916, "is_counter": true}, "livewire": {"data": {"app.filament.staff.resources.overview-resource.pages.manage-overview #aEol6nkhOSBXF4nMtXlP": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => \"3\"\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"16\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.staff.resources.overview-resource.pages.manage-overview\"\n  \"component\" => \"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\"\n  \"id\" => \"aEol6nkhOSBXF4nMtXlP\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview@loadTable<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanDeferLoading.php&line=17\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/CanDeferLoading.php:17-20</a>", "middleware": "web", "duration": "2.24s", "peak_memory": "16MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1429790986 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1429790986\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-557497119 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bkA39rqFwVIe1D0ZdM7UNt4Pt9VbWxl4BTDdXTI7</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1883 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:false,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:&quot;3&quot;,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;16&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;aEol6nkhOSBXF4nMtXlP&quot;,&quot;name&quot;:&quot;app.filament.staff.resources.overview-resource.pages.manage-overview&quot;,&quot;path&quot;:&quot;staff\\/overviews&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;263f9d5dc72ac33653faf9bd26aa32fa81a6ba9ab19a7138f9fcfcac7c1a584d&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"9 characters\">loadTable</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-557497119\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IjRZRGdvOXFmSWIyZzVFS2hlSFlkN2c9PSIsInZhbHVlIjoiQlNOWHFJMHM5dWdhZTdSR3dwOWY1K05waWlTYW5uekNmZ1h5am5VUXkwaENUUDRBMzRPOUZRYTlSK0t5Q1loSC9LK2NnUFB2emRNSE5FYS9ScW1FdEVnWEl0WnZwd0JEdW1YNDQwelQvZ3gzSnkvdDB1V0dQM2YxY1hzYU44cmsiLCJtYWMiOiIzM2I1OTA1N2RmZDQ1MDY2Njk1ZWQ1MmI2ODRiZTRmY2VjMWY4NzczOGI2NGUxYmQ1NWJmYTAxNjU2NWIyMGY5IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IjdNRkJIeGYycjhwOGFUV3RzZ2F4eXc9PSIsInZhbHVlIjoiQ0VhNVZzcE9wMUJBT3lUM3BpTFJBVnRSTXJqcG1MdlFYZkgvemVhMVc3ejZRWFdtcDVldmpuU1ZCdWJzam82TWZzQUlDaElDdS9tSVo4U3pSV053M1MxYU01Z0lHbnBVTW9ZZGs0aWhGMzBhN0JpY0pJUHdoYnUzZk5scm9lN2IiLCJtYWMiOiI3MDE4MTBhN2EwYzNkMTQzMGFjZTU2MzlkMmY1NmYwY2ViZDczY2UzNjgyYTBmMzkyZTFkNjk2ZTcxYWE3ZWUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"228 characters\">https://portal.racoed.test/staff/overviews?tableFilters[overview_filter][school_session_id]=3&amp;tableFilters[overview_filter][semester_id]=1&amp;tableFilters[overview_filter][level_id]=1&amp;tableFilters[overview_filter][department_id]=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2276</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1481916708 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bkA39rqFwVIe1D0ZdM7UNt4Pt9VbWxl4BTDdXTI7</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aYGnG8gbrucmIdT9M2DJDYTxkt3PKUtjFTE7Fv4h</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1481916708\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1354069322 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 19:02:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354069322\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1266770719 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">bkA39rqFwVIe1D0ZdM7UNt4Pt9VbWxl4BTDdXTI7</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K0MH66VX1Y4NKBHY5K2RDDY9</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"260 characters\">https://portal.racoed.test/staff/overviews?tableFilters%5Boverview_filter%5D%5Bschool_session_id%5D=3&amp;tableFilters%5Boverview_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Boverview_filter%5D%5Blevel_id%5D=1&amp;tableFilters%5Boverview_filter%5D%5Bdepartment_id%5D=16</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1266770719\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}