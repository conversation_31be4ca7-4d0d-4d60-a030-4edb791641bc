<?php

namespace App\Filament\Staff\Resources\StudentResource\Pages;

use App\Enums\Role;
use Filament\Actions;
use Livewire\Livewire;
use App\Enums\AdmissionStatus;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\File;
use Filament\Resources\Components\Tab;
use Filament\Notifications\Notification;
use App\Filament\Imports\StudentImporter;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Staff\Resources\StudentResource;

class ListStudents extends ListRecords
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->visible(fn() => Auth::user()->role === Role::ICT)
                ->label('Import Students')
                ->importer(StudentImporter::class)
                ->maxRows(100)
                ->fileRules([
                    File::types(['csv', 'txt'])->max(1024),
                ]),
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        $livewire = Livewire::current();
        $filters = self::extractFilters($livewire);
        $filteredSessionId = $filters['school_session_id'] ?? null;
        $filteredSemesterId = $filters['semester_id'] ?? null;

        $registeredQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereDoesntHave('registrations', fn($q) => $q->where('is_graduated', true))
            ->whereDoesntHave('registrations', fn($q) => $q->where('is_withdrawn', true));

        $pendingQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::PENDING));

        $deniedQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::DENIED));

        $graduatedQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereHas('registrations', fn($q) => $q->where('is_graduated', true));

        $withdrawnQuery = fn(Builder $query) => $query
            ->whereHas('application', fn($q) => $q->where('admission_status', AdmissionStatus::APPROVED))
            ->whereHas('registrations', fn($q) => $q->where('is_withdrawn', true));

        $getBadgeCount = function (callable $queryModifier) use ($filteredSessionId, $filteredSemesterId): int {
            $query = $this->getResource()::getEloquentQuery();

            $queryModifier($query);

            if ($filteredSessionId && $filteredSemesterId) {
                $query->whereHas('registrations', fn($q) => $q->where('school_session_id', $filteredSessionId)->where('semester_id', $filteredSemesterId));
            }

            return $query->count();
        };

        return [
            'registered' => Tab::make('registered')
                ->label('Registered')
                ->modifyQueryUsing($registeredQuery)
                ->badge($getBadgeCount($registeredQuery)),

            'pending' => Tab::make('pending')
                ->label('Pending')
                ->modifyQueryUsing($pendingQuery)
                ->badge($getBadgeCount($pendingQuery)),

            'denied' => Tab::make('denied')
                ->label('Denied')
                ->modifyQueryUsing($deniedQuery)
                ->badge($getBadgeCount($deniedQuery)),

            'graduated' => Tab::make('graduated')
                ->label('Graduated')
                ->modifyQueryUsing($graduatedQuery)
                ->badge($getBadgeCount($graduatedQuery)),

            'withdrawn' => Tab::make('withdrawn')
                ->label('Withdrawn')
                ->modifyQueryUsing($withdrawnQuery)
                ->badge($getBadgeCount($withdrawnQuery)),
        ];
    }

    private static function extractFilters($livewire): array
    {
        $filters = $livewire->tableFilters['session_semester_level_programme_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'programme_id' => $filters['programme_id'] ?? null,
        ];
    }

    public function printRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.print', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("(function() {
                const newWindow = window.open(
                    '$url',
                    'Registration',
                    'width=800,height=600,toolbar=no,menubar=no,scrollbars=yes,resizable=yes,status=no'
                );
            
                if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                    alert('Pop-up blocked! Please allow pop-ups to print the report card.');
                } else {
                    newWindow.focus();
                }
            })();");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Notification::make()
                ->title('Print Error')
                ->body('Unable to print Registration. Please try again later.' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function downloadRegistration($registrationId)
    {
        try {
            $url = URL::signedRoute('registration.download', ['registration' => $registrationId]);

            // If this is a Livewire component
            return $this->js("window.location.href = '$url';");

            // Or if you're using newer Livewire syntax
            // return $this->dispatch('openWindow', url: $url);

        } catch (\Exception $e) {
            Notification::make()
                ->title('Download Error')
                ->body('Unable to download Registration. Please try again later.' . $e->getMessage())
                ->danger()
                ->send();
        }
    }


    // public function confirmPromotionsAction(): Action
    // {
    //     return Action::make('confirmPromotions')
    //         ->requiresConfirmation()
    //         ->modalHeading('Confirm promotions?')
    //         ->modalWidth(MaxWidth::TwoExtraLarge)
    //         ->modalDescription(function (array $arguments) {
    //             $tableRecords = $arguments['tableRecords'];
    //             $enrollmentData = $arguments['enrollmentData'];

    //             $numberOfStudents = count($tableRecords);

    //             $tableRecord = $tableRecords[0] ?? [];

    //             $currentSession = SchoolSession::find($tableRecord['school_session_id'])?->name ?? '';
    //             $currentClass = SchoolClass::find($tableRecord['school_class_id'])?->name ?? '';
    //             $currentSection = Section::find($tableRecord['section_id'])?->name ?? '';

    //             $newSession = SchoolSession::find($enrollmentData['school_session_id'])?->name ?? '';
    //             $newClass = SchoolClass::find($enrollmentData['school_class_id'])?->name ?? '';
    //             $newSection = Section::find($enrollmentData['section_id'])?->name ?? '';

    //             if ($tableRecord['school_session_id'] == $enrollmentData['school_session_id']) {
    //                 return new HtmlString("
    //                     Are you sure you want to update the enrollment of <b>{$numberOfStudents} " . Str::plural('student', $numberOfStudents) . "</b>
    //                     <br>in <b>{$newSession}</b> session from:
    //                     <br><b>{$currentClass}</b> class, <b>{$currentSection}</b> section
    //                     <br>to:
    //                     <br><b>{$newClass}</b> class, <b>{$newSection}</b> section?
    //                     <br><br><i>This action cannot be undone.</i>
    //                 ");
    //             }

    //             return new HtmlString("
    //                 Are you sure you want to promote <b>{$numberOfStudents} " . Str::plural('student', $numberOfStudents) . "</b> from:
    //                 <br><b>{$currentSession}</b> session,
    //                 <b>{$currentClass}</b> class,
    //                 <b>{$currentSection}</b> section
    //                 <br>to:
    //                 <br><b>{$newSession}</b> session,
    //                 <b>{$newClass}</b> class,
    //                 <b>{$newSection}</b> section?
    //                 <br><br><i>This action cannot be undone.</i>
    //             ");
    //         })
    //         ->action(function ($arguments) {
    //             $tableRecords = $arguments['tableRecords'];
    //             $enrollmentData = $arguments['enrollmentData'];

    //             $enrollmentData['school_id'] = Filament::getTenant()->id;
    //             $enrollmentData['status'] = true;
    //             unset($enrollmentData['has_mismatch']);

    //             foreach ($tableRecords as $student) {
    //                 $student = User::find($student['id']);
    //                 if (
    //                     $student && $student->registrations()
    //                     ->where('school_session_id', $enrollmentData['school_session_id'])
    //                     ->where('school_class_id', $enrollmentData['school_class_id'])
    //                     ->where('section_id', $enrollmentData['section_id'])
    //                     ->exists()
    //                 ) {
    //                     Notification::make()
    //                         ->danger()
    //                         ->title('Operation failed')
    //                         ->body(new HtmlString('Some students are already registered in the selected <b>session</b>, <b>class</b>, and <b>section</b>.'))
    //                         ->send();
    //                     return;
    //                 }
    //             }

    //             $studentCounts = DB::registration(function () use ($tableRecords, $enrollmentData) {
    //                 $updated = 0;
    //                 $created = 0;

    //                 foreach ($tableRecords as $student) {
    //                     $student = User::find($student['id']);
    //                     if ($student) {
    //                         $sameSession = $student->registrations()
    //                             ->where('school_session_id', $enrollmentData['school_session_id'])
    //                             ->first();

    //                         $student->registrations()
    //                             ->when($sameSession, fn($q) => $q->where('id', '!=', optional($sameSession)->id))
    //                             ->update(['status' => false]);

    //                         if ($sameSession) {
    //                             $sameSession->update([
    //                                 'school_class_id' => $enrollmentData['school_class_id'],
    //                                 'section_id'      => $enrollmentData['section_id'],
    //                                 'status'          => true,
    //                             ]);
    //                             $updated++;
    //                         } else {
    //                             $student->registrations()->create($enrollmentData);
    //                             $created++;
    //                         }
    //                     }
    //                 }

    //                 return ['updated' => $updated, 'created' => $created];
    //             });

    //             $newSession = SchoolSession::find($enrollmentData['school_session_id'])?->name ?? '';
    //             $newClass = SchoolClass::find($enrollmentData['school_class_id'])?->name ?? '';
    //             $newSection = Section::find($enrollmentData['section_id'])?->name ?? '';

    //             if ($studentCounts['created']) {
    //                 Notification::make()
    //                     ->success()
    //                     ->title('Students Promoted')
    //                     ->body("<b>{$studentCounts['created']} " . Str::plural('student', $studentCounts['created']) . "</b> promoted to <b>{$newSession}</b> session, <b>{$newClass}</b> class, and <b>{$newSection}</b> section successfully.")
    //                     ->send();
    //             }

    //             if ($studentCounts['updated']) {
    //                 Notification::make()
    //                     ->success()
    //                     ->title('Registrations Updated')
    //                     ->body("<b>{$studentCounts['updated']} " . Str::plural('student', $studentCounts['updated']) . "</b> enrollment updated for <b>{$newSession}</b> session, <b>{$newClass}</b> class, and <b>{$newSection}</b> section successfully.")
    //                     ->send();
    //             }
    //         });
    // }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'registered';
    }
}
