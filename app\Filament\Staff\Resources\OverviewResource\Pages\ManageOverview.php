<?php

namespace App\Filament\Staff\Resources\OverviewResource\Pages;

use App\Filament\Staff\Resources\OverviewResource;
use Filament\Resources\Pages\ManageRecords;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\HtmlString;

class ManageOverview extends ManageRecords
{
    protected static string $resource = OverviewResource::class;

    protected ?array $cachedColumns = null;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function table(Table $table): Table
    {
        $table = parent::table($table);

        // Override the columns with dynamic ones
        return $table->columns($this->getTableColumns());
    }

    public function getTable(): Table
    {
        // Force fresh table configuration on each call
        return $this->table($this->makeTable());
    }

    protected function getTableColumns(): array
    {
        // Use cached columns if available, otherwise build fresh
        if ($this->cachedColumns === null) {
            $this->cachedColumns = $this->buildTableColumns();
        }

        return $this->cachedColumns;
    }

    private function buildTableColumns(): array
    {
        // Get the base columns from the resource
        $baseColumns = [
            TextColumn::make('#')
                ->rowIndex(),
            TextColumn::make('name')
                ->sortable(
                    query: fn($query, $direction) =>
                    $query->orderBy("users.last_name", $direction)
                )
                ->searchable(
                    query: fn($query, $search) =>
                    $query->whereRaw("CONCAT(users.last_name, ' ', users.first_name, ' ', users.middle_name) LIKE ?", ["%{$search}%"])
                ),
            TextColumn::make('matric_number')
                ->label('Matric no.'),
            ColumnGroup::make(function () {
                $courses = OverviewResource::getCourses($this);
                $totalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($courses) ?? 0;
                return 'Semester summary (Total Credit Unit: ' . $totalCreditUnit . ')';
            })
                ->columns([
                    TextColumn::make('gpa')
                        ->label(new HtmlString("<div x-tooltip=\"'Grade Point Average'\">GPA</div>"))
                        ->state(function ($record) {
                            $courses = OverviewResource::getCourses($this);
                            $totalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($courses) ?? 0;
                            $gradePointAverage = $totalCreditUnit > 0  ? number_format(OverviewResource::getSemesterTotalGradePoint($this, $record, $courses) / $totalCreditUnit, 2) : 0;
                            $record->calculatedGradePointAverage = $gradePointAverage;
                            return $gradePointAverage;
                        }),
                    TextColumn::make('remark')
                        ->state(fn($record) => OverviewResource::getRemarkFromGradePointAverage($record->calculatedGradePointAverage)?->remark),
                    TextColumn::make('outstanding')
                        ->words(4)
                        ->tooltip(function ($state): ?string {
                            // Count course codes by splitting on comma
                            $courseCount = count(array_filter(explode(',', $state)));

                            if ($courseCount <= 2) { // Adjust this number based on how many courses you want to show
                                return null;
                            }

                            return $state;
                        })
                        ->label(new HtmlString("<div x-tooltip=\"'Carry-over courses'\">Outstanding</div>"))
                        ->state(fn($record) => OverviewResource::getSemesterOutstandingCourses($record, $this)->pluck('code')->implode(', ')),
                ])->alignment(Alignment::Center),
        ];

        // Only get course columns if we have the required filters
        if (OverviewResource::hasRequiredFilters($this)) {
            $courseColumns = OverviewResource::getCourseColumns($this);
            return array_merge($baseColumns, $courseColumns);
        }

        return $baseColumns;
    }

    public function updatedTableFilters(): void
    {
        // Clear cached columns to force fresh evaluation
        $this->cachedColumns = null;

        // Force a complete table rebuild with new filter values
        $this->resetTable();

        // Force re-evaluation of table columns by clearing any cached data
        $this->dispatch('$refresh');
    }

    // Override this method to ensure fresh column evaluation
    public function getCachedTable(): Table
    {
        // Don't cache the table - always rebuild it fresh
        return $this->getTable();
    }
}
