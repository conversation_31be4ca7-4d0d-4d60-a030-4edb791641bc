<?php

namespace App\Filament\Staff\Resources\OverviewResource\Pages;

use App\Filament\Staff\Resources\OverviewResource;
use Filament\Resources\Pages\ManageRecords;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;

class ManageOverview extends ManageRecords
{
    protected static string $resource = OverviewResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function table(Table $table): Table
    {
        $table = parent::table($table);

        // Override the columns with dynamic ones
        return $table->columns($this->getTableColumns());
    }

    public function makeTable(): Table
    {
        // Create a fresh table instance each time
        return app(Table::class);
    }

    protected function getTableColumns(): array
    {
        // Always build fresh columns - don't cache during filter changes
        return $this->buildTableColumns();
    }

    private function buildTableColumns(): array
    {
        // Get the base columns from the resource
        $baseColumns = [
            TextColumn::make('#')
                ->rowIndex(),
            TextColumn::make('name')
                ->sortable(
                    query: fn($query, $direction) =>
                    $query->orderBy("users.last_name", $direction)
                )
                ->searchable(
                    query: fn($query, $search) =>
                    $query->whereRaw("CONCAT(users.last_name, ' ', users.first_name, ' ', users.middle_name) LIKE ?", ["%{$search}%"])
                ),
            TextColumn::make('matric_number')
                ->label('Matric no.'),
            ColumnGroup::make(function () {
                $courses = OverviewResource::getCourses($this);
                $totalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($courses) ?? 0;
                return 'Semester summary (Total Credit Unit: ' . $totalCreditUnit . ')';
            })
                ->columns([
                    TextColumn::make('gpa')
                        ->label(new HtmlString("<div x-tooltip=\"'Grade Point Average'\">GPA</div>"))
                        ->state(function ($record) {
                            $courses = OverviewResource::getCourses($this);
                            $totalCreditUnit = OverviewResource::getSemesterTotalCreditUnit($courses) ?? 0;
                            $gradePointAverage = $totalCreditUnit > 0  ? number_format(OverviewResource::getSemesterTotalGradePoint($this, $record, $courses) / $totalCreditUnit, 2) : 0;
                            $record->calculatedGradePointAverage = $gradePointAverage;
                            return $gradePointAverage;
                        }),
                    TextColumn::make('remark')
                        ->state(fn($record) => OverviewResource::getRemarkFromGradePointAverage($record->calculatedGradePointAverage)?->remark),
                    TextColumn::make('outstanding')
                        ->words(4)
                        ->tooltip(function ($state): ?string {
                            // Count course codes by splitting on comma
                            $courseCount = count(array_filter(explode(',', $state)));

                            if ($courseCount <= 2) { // Adjust this number based on how many courses you want to show
                                return null;
                            }

                            return $state;
                        })
                        ->label(new HtmlString("<div x-tooltip=\"'Carry-over courses'\">Outstanding</div>"))
                        ->state(fn($record) => OverviewResource::getSemesterOutstandingCourses($record, $this)->pluck('code')->implode(', ')),
                ])->alignment(Alignment::Center),
        ];

        // Add course columns dynamically - but only if we have the required filters
        if (OverviewResource::hasRequiredFilters($this)) {
            // Get courses based on current filter state
            $courses = OverviewResource::getCourses($this);

            // Debug: Log the number of courses found
            Log::info('Building table columns - Found courses: ' . $courses->count());

            // Create course columns dynamically
            foreach ($courses as $course) {
                $baseColumns[] = $this->createCourseColumn($course);
            }
        } else {
            Log::info('Building table columns - Required filters not met');
        }

        return $baseColumns;
    }

    private function createCourseColumn($course): ColumnGroup
    {
        $failedScore = OverviewResource::getFailedScore();

        return ColumnGroup::make("title.{$course->id}")
            ->label(new HtmlString("<div x-tooltip=\"'{$course->title}'\">{$course->code} <span class='text-gray-400'>{$course->credit}{$course->course_status->value}</span></div>"))
            ->columns([
                TextColumn::make("score.{$course->id}")
                    ->label(new HtmlString("<div x-tooltip=\"'Score'\"> S </div>"))
                    ->color(fn($state): string => ($state <= $failedScore) ? 'danger' : 'black')
                    ->state(function ($record) use ($course) {
                        $courseData = OverviewResource::getCourseData($this, $record, $course);
                        $record->courseData = $courseData;
                        return $courseData['score'] ?? '-';
                    }),
                TextColumn::make("grade.{$course->id}")
                    ->label(new HtmlString("<div x-tooltip=\"'Grade'\">G</div>"))
                    ->color(fn($record): string => ($record->courseData['score'] <= $failedScore) ? 'danger' : 'black')
                    ->state(fn($record) => $record->courseData['grade'] ?? '-'),
                TextColumn::make("point.{$course->id}")
                    ->label(new HtmlString("<div x-tooltip=\"'Point'\">P</div>"))
                    ->state(fn($record) => $record->courseData['point'] ?? '-'),
                TextColumn::make("grade_point.{$course->id}")
                    ->label(new HtmlString("<div x-tooltip=\"'Grade Point'\">GP</div>"))
                    ->state(fn($record) => $record->courseData['grade_point'] ?? '-'),
            ])
            ->alignment(Alignment::Center);
    }

    public function updatedTableFilters(): void
    {
        Log::info('Table filters updated', [
            'filters' => $this->tableFilters,
            'hasRequiredFilters' => OverviewResource::hasRequiredFilters($this)
        ]);

        // Force a complete table rebuild with new filter values
        $this->resetTable();

        // Force re-evaluation of table columns
        $this->dispatch('$refresh');
    }

    // Override this method to ensure fresh column evaluation
    public function getCachedTable(): Table
    {
        // Don't cache the table - always rebuild it fresh
        return $this->getTable();
    }
}
