
<x-slot name="title">{{ __('Campus News - RACOED') }}</x-slot>

<section class="bg-white py-10">
    <div class="max-w-full sm:max-w-3xl md:max-w-5xl lg:max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl font-semibold text-[#96281B] mb-6 text-center">Campus News</h2>
        @if ($news->count())
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                @foreach ($news as $item)
                    <div class="bg-gray-100 rounded-sm shadow p-4">
                        <img src="{{ asset('storage/' . $item->image) }}" alt="{{ $item->title }}" class="w-full h-40 object-cover rounded mb-3">
                        <h3 class="text-lg font-semibold text-[#96281B]">{{ $item->title }}</h3>
                    <p class="text-sm text-gray-600 mb-2">{{ \Carbon\Carbon::parse($item->date)->toFormattedDateString() }}</p>
                    <p class="text-gray-700 text-sm line-clamp-3">{!! str($item->content)->markdown()->sanitizeHtml()->limit(100) !!}</p>
                    <a href="{{ route('news.show', $item->slug) }}" class="text-[#96281B] text-sm font-medium mt-2 inline-block">Read More →</a>
                </div>
            @endforeach
        </div>
        @else
            <p class="text-center text-gray-600">No campus news available at the moment.</p>
        @endif
        <div class="mt-8 text-center">
            {{ $news->links() }}
        </div>
    </div>
</section>



