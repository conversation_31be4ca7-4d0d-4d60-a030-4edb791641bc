<?php

namespace App\Filament\Components;

use App\Enums\Role;
use App\Enums\Title;
use App\Models\Level;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Enums\ExamBoard;
use App\Enums\WaecGrade;
use App\Models\Semester;
use App\Models\Programme;
use App\Enums\WaecSubject;
use App\Enums\AddressState;
use App\Enums\Qualification;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use App\Enums\GuardianOccupation;
use Illuminate\Support\HtmlString;
use App\Enums\GuardianRelationship;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Illuminate\Database\Eloquent\Builder;

class StaffForm
{
    public static function schema(): array
    {
        return [
            Section::make('A. Login Details')
                ->description(fn(string $operation) => $operation === 'view' ?
                    "The staff's login information."
                    : "Kindly provide the staff's login information.")
                ->schema([
                    Grid::make(['default' => 2, 'md' => 3])
                        ->schema([
                            TextInput::make('phone')
                                ->required()
                                ->unique(ignoreRecord: true)
                                ->tel()
                                ->autocomplete('tel')
                                ->maxLength(11)
                                ->autofocus(fn(string $operation) => $operation === 'create')
                                ->prefixIcon('heroicon-m-phone')
                                ->placeholder(fn($operation) => $operation === 'view' ? null : '07042366520'),
                            TextInput::make('password')
                                ->visibleOn('create')
                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Kindly set a simple password that is easy to remember.')
                                ->revealable()
                                ->confirmed()
                                ->password()
                                ->required(fn(string $operation) => $operation === 'create'),
                            TextInput::make('password_confirmation')
                                ->visibleOn('create')
                                ->dehydrated(false)
                                ->revealable()
                                ->password()
                                ->required(fn(string $operation) => $operation === 'create')
                        ])
                ]),
            Section::make('B. Personal Details')
                ->description(fn(string $operation) => $operation === 'view' ?
                    "The staff's personal information."
                    : "Kindly provide the staff's personal information.")
                ->schema([
                    Grid::make(['default' => 2, 'md' => 3])
                        ->schema([
                            Select::make('title')
                                ->options(Title::class)
                                ->native(false)
                                ->required(),
                            TextInput::make('last_name')
                                ->required()
                                ->maxLength(20)
                                ->label('Surname')
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Hamzat'),
                            TextInput::make('first_name')
                                ->required()
                                ->maxLength(20)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Mikail'),
                            TextInput::make('middle_name')
                                ->maxLength(20)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Olatunji'),
                            Select::make('qualification')
                                ->options(Qualification::class)
                                ->multiple()
                                ->native(false)
                                ->required(fn(string $operation) => $operation === 'create'),
                            DatePicker::make('date_of_birth')
                                ->label('Date of birth')
                                ->prefixIcon('heroicon-m-calendar-days')
                                ->closeOnDateSelection()
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Pick a date')
                                ->minDate(now()->subYears(50))
                                ->maxDate(now())
                                ->native(false),
                            Select::make('gender')
                                ->required(fn(string $operation) => $operation === 'create')
                                ->options([
                                    'male' => 'Male',
                                    'female' => 'Female',
                                ])
                                ->native(false),
                            Select::make('marital_status')
                                ->required(fn(string $operation) => $operation === 'create')
                                ->options([
                                    'single' => 'Single',
                                    'married' => 'Married',
                                    'divorced' => 'Divorced',
                                    'widowed' => 'Widowed',
                                ])
                                ->native(false),
                            Select::make('religion')
                                ->required(fn(string $operation) => $operation === 'create')
                                ->options([
                                    'christianity' => 'Christianity',
                                    'islam' => 'Islam',
                                    'other' => 'Other',
                                ])
                                ->native(false),
                            TextInput::make('email')
                                ->required()
                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Your email is needed for online payment and password reset.')
                                ->email()
                                ->unique(ignoreRecord: true)
                                ->rules(['email:rfc,dns'])
                                ->placeholder(fn($operation) => $operation === 'view' ? null : '<EMAIL>'),
                            TextInput::make('address_line')
                                ->required(fn(string $operation) => $operation === 'create')
                                ->maxLength(30)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'No. 1 Osolo Way'),
                            TextInput::make('address_town')
                                ->required(fn(string $operation) => $operation === 'create')
                                ->label('Town')
                                ->maxLength(15)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Osogbo'),
                            Select::make('address_state')
                                ->required(fn(string $operation) => $operation === 'create')
                                ->label('State')
                                ->searchable()
                                ->preload()
                                ->options(AddressState::class)
                                ->native(false),
                            Select::make('role')
                                ->visibleOn('create')
                                ->label('Post')
                                ->live()
                                ->required()
                                ->native(false)
                                ->options(
                                    collect(Role::cases())
                                        ->reject(fn($role) => in_array($role, [Role::STUDENT, Role::ICT]))
                                        ->mapWithKeys(fn($role) => [$role->value => $role->getLabel()])
                                ),

                            Select::make('department_id')
                                ->label('Department')
                                ->required(fn(string $operation) => $operation === 'create')
                                ->native(false)
                                ->visible(fn(Get $get, $operation) => (int) $get('role') === Role::HOD->value && $operation === 'create')
                                ->relationship('department', 'name'),
                            FileUpload::make('photo')
                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Photo size must be between 5kb-200kb.')
                                ->label('Passport photo')
                                ->directory('photos')
                                ->image()
                                ->imageCropAspectRatio('1:1')
                                ->minSize(5)
                                ->maxSize(200)
                                ->uploadingMessage('Uploading passport photo...'),
                        ])
                ]),

        ];
    }
}
