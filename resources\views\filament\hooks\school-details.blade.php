 @php
    use App\Settings\CollegeSettings;
    use App\Models\SemesterSchedule;

$panelId = filament()->getCurrentPanel()?->getId();

$activeSchoolSession = activeSchoolSession();
$activeSchoolSessionName = $activeSchoolSession ? $activeSchoolSession->name : 'no session';

$activeSemester = 'Holiday';
$upcomingSemester = null;

if ($activeSchoolSession) {
    $currentDate = now();
    $semesterSchedules = SemesterSchedule::where('school_session_id', $activeSchoolSession->id)
        ->with('semester') 
        ->orderBy('semester_start', 'asc')
        ->get();

    if ($semesterSchedules->isEmpty()) {
        $activeSemester = 'No semester schedules';
    } else {
        foreach ($semesterSchedules as $semesterSchedule) {
            if ($currentDate->between($semesterSchedule->semester_start, $semesterSchedule->semester_end)) {
                $activeSemester = $semesterSchedule->semester->name;
                break;
            }

            if ($currentDate->lt($semesterSchedule->semester_start)) {
                $upcomingSemester = $semesterSchedule;
                break;
            }
        }

        if ($activeSemester === 'Holiday' && isset($upcomingSemester)) {
            $SemesterStart = new \DateTime($upcomingSemester->semester_start);
            $activeSemester .= " ({$upcomingSemester->semester->name} begins on {$semesterStart->format('M j Y')})";
        }
    }
}
@endphp

@if ($panelId !== 'help')
<dev class="hidden lg:flex flex-col text-xs text-gray-600 dark:text-gray-200">
    <a href="{{ config('app.url') }}" class="font-semibold mb-2">{{ app(CollegeSettings::class)->name }}</a>
    <div>
        <span class="font-semibold">Session:</span>
        <span>
            {{ $activeSchoolSessionName }} |
        </span>
        <span class="font-semibold">Semester:</span>
        <span>
            {{ $activeSemester }}
        </span>
    </div>
</dev>
@endif
