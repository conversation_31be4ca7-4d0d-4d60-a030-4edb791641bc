<?php

namespace App\Filament\Staff\Resources;

use App\Enums\Role;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use App\Filament\Components\StaffForm;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\ImageColumn;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Staff\Resources\StaffResource\Pages;
use App\Filament\Staff\Resources\StaffResource\RelationManagers;

class StaffResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?string $modelLabel = 'staff';
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationGroup = 'User';
    protected static ?string $navigationBadgeTooltip = 'Total number of staffs';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('users.role', '!=', Role::STUDENT);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function canAccess(): bool
    {
        return management_staff_access();
    }

    public static function canEdit($record): bool
    {
        return ict_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return ict_access();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema(StaffForm::schema());
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->searchPlaceholder('Search (Name)')
            ->striped()
            ->paginated([10, 20, 50])
            ->defaultSort('last_name')
            ->recordUrl(null)
            ->recordAction(null)
            ->emptyStateHeading('No Staff Yet')
            ->emptyStateDescription('Once you create your staff, it will appear here.')
            ->description("Staff members manage daily operations, oversee financial records, handle student data, and maintain institutional integrity.")
            ->columns([
                TextColumn::make('#')
                    ->rowIndex()
                    ->width('2rem'),
                ImageColumn::make('photo')
                    ->defaultImageUrl(fn($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->name))
                    ->label('Passport')
                    ->extraImgAttributes(['style' => 'border-radius: 0.125rem;'])
                    ->size('3rem'),
                TextColumn::make('name')
                    ->searchable(
                        query: fn($query, $search) =>
                        $query->whereRaw("CONCAT(users.last_name, ' ', users.first_name, ' ', users.middle_name) LIKE ?", ["%{$search}%"])
                    )
                    ->sortable(),
                TextColumn::make('phone')
                    ->icon('heroicon-m-phone'),
                TextColumn::make('department.name')
                    ->label('Department')
                    ->placeholder('NIL'),
                TextColumn::make('role')
                    ->label('Post')
                    ->badge(),
            ])

            ->actions([
                ActionGroup::make([
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('Admin Updated')
                                ->body('The admin has been saved successfully.');
                        }),
                    Action::make('resetPassword')
                        ->visible(fn() => main_staff_access())
                        ->label('Reset password')
                        ->icon('heroicon-o-arrow-path')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('Reset Password?')
                        ->modalDescription(new HtmlString("Are you sure you'd like to reset this staff's login password to <strong>1234</strong>? This cannot be undone."))
                        ->modalSubmitActionLabel('Reset')
                        ->action(function (User $record) {
                            $record->password = bcrypt('1234');
                            $record->save();

                            Notification::make()
                                ->success()
                                ->title('Password Reset')
                                ->body("The staff's login password has been reset successfully.")
                                ->send();
                        }),
                    Tables\Actions\DeleteAction::make()
                        ->visible(fn() => main_staff_access())
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Admin Deleted')
                                ->body('The admin has been deleted successfully.'),
                        ),
                ])
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStaffs::route('/'),
            'create' => Pages\CreateStaff::route('/create'),
            'view' => Pages\ViewStaff::route('/{record}'),
            'edit' => Pages\EditStaff::route('/{record}/edit'),
        ];
    }
}
