@php
use App\Enums\AdmissionStatus;
use App\Settings\AdmissionSettings;

$admissionSettings = app(AdmissionSettings::class);
@endphp

<div class="flex flex-col sm:flex-row gap-8 sm:gap-4">

    <!-- Left div  -->
    <div class="order-2 sm:order-1 flex-1 max-w-lg space-y-4">

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Application number:
                </span>
                <span class="text-gray-900 text-sm">
                    {{ $getRecord()->application->number ?? 'NIL'}}
                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Entry Programme:
                </span>
                <span class="text-gray-900 text-sm">
                    {{ $getRecord()->application->programme->name ?? 'NIL' }}
                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Screening score:
                </span>
                <span class="text-gray-900 text-sm">
                    {{ $getRecord()->application->screening_score ?? 'NIL' }}
                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Screening status:
                </span>
                <span class="text-gray-900 text-sm">
                    @if ($getRecord()->application->screening_status)
                    <x-filament::badge :color="$getRecord()->application->screening_status->getColor()">
                        {{ $getRecord()->application->screening_status->getLabel() }}
                    </x-filament::badge>
                    @else
                    NIL
                    @endif

                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Admission status:
                </span>
                <span class="text-gray-900 text-sm">
                    @if($getRecord()->application->admission_status)
                    <x-filament::badge :color="$getRecord()->application->admission_status->getColor()">
                        {{ $getRecord()->application->admission_status->getLabel() }}
                    </x-filament::badge>
                    @else
                    NIL
                    @endif
                </span>
            </div>
        </div>

        <div class="bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
            <div class="flex flex-col sm:flex-row sm:items-center gap-2">
                <span class="font-bold text-gray-700 text-sm min-w-[140px]">
                    Admission date:
                </span>
                <span class="text-gray-900 text-sm">
                    {{ $getRecord()->application->admission_date ? \Carbon\Carbon::parse($getRecord()->application->admission_date)->format('d M, Y') : 'NIL' }}
                </span>
                
            </div>
        </div>
    </div>

    <!-- Right div  -->
    @if ($getRecord()->application->admission_status === AdmissionStatus::PENDING)

    <div class="order-1 sm:order-2 flex-1">
        <div class=" flex items-center justify-center pb-2">
            <x-heroicon-s-clock  class="w-6 h-6 text-gray-500" />
        </div>
        <h2 class="font-bold text-center text-gray-700">ADMISSION IN PROGRESS
        </h2>
        <p class="text-gray-900 text-center text-sm">Dear applicant, your admission is currently under
            processing.<br>Thank you for your patience. </p>
            {{-- APPLICATION DATA --}}
            <div class="flex justify-center gap-4" style="margin-top: 1rem">
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-filament::button tooltip="Export application data">
                            Application data
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item wire:click="printApplicationData('{{ $getRecord()->id }}')"
                            wire:loading.attr="disabled">
                            <div class="flex items-center gap-2">
                                <x-heroicon-s-printer class="w-4 h-4" />
                                Print
                            </div>
                        </x-filament::dropdown.list.item>
                        <x-filament::dropdown.list.item wire:click="downloadApplicationData('{{ $getRecord()->id }}')"
                            wire:loading.attr="disabled">
                            <div class="flex items-center gap-2">
                                <x-heroicon-s-document-arrow-down class="w-4 h-4" />
                                Download
                            </div>
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>

    </div>

    @elseif ($getRecord()->application->admission_status === AdmissionStatus::APPROVED)
    <div class="flex-1 bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
        <div class=" flex items-center justify-center pb-2">
            <x-heroicon-s-check-circle :style="'stroke: rgb(34 197 94)'" class="w-6 h-6" />
        </div>
        <h2 class="font-bold text-center text-gray-700">CONGRATULATIONS!!!
        </h2>
        <p class="text-gray-900 text-center text-sm">You have been offered admission into our college.<br> We are excited
            to see all you will achieve.<br> Welcome aboard! </p>

        {{-- APPLICATION DATA --}}
            <div class="flex justify-center gap-4" style="margin-top: 1rem">
                <x-filament::dropdown>
                    <x-slot name="trigger">
                        <x-filament::button tooltip="Export application data">
                            Application data
                        </x-filament::button>
                    </x-slot>
                    <x-filament::dropdown.list>
                        <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printApplicationData({{ $getRecord()->id }})">                              
                                    Print                              
                        </x-filament::dropdown.list.item>
                        <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadApplicationData({{ $getRecord()->id }})">                              
                                Download                                
                        </x-filament::dropdown.list.item>
                    </x-filament::dropdown.list>
                </x-filament::dropdown>
            </div>
        
        {{-- ADMISSION LETTER --}}
        <div class="flex justify-center gap-4" style="margin-top: 1rem">
            <x-filament::dropdown>
                <x-slot name="trigger">
                    <x-filament::button tooltip="Export admission letter">
                        Admission letter
                    </x-filament::button>
                </x-slot>
                <x-filament::dropdown.list>
                    <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printAdmissionLetter({{ $getRecord()->id }})">
                       Print
                    </x-filament::dropdown.list.item>
                    <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadAdmissionLetter({{ $getRecord()->id }})">
                        Download
                    </x-filament::dropdown.list.item>
                </x-filament::dropdown.list>
            </x-filament::dropdown>
        </div>
        {{-- ACCEPTANCE LETTER --}}
        <div class="flex justify-center gap-4" style="margin-top: 1rem">
            <x-filament::dropdown>
                <x-slot name="trigger">
                    <x-filament::button tooltip="Export acceptance letter">
                        Acceptance letter
                    </x-filament::button>
                </x-slot>
                <x-filament::dropdown.list>
                    <x-filament::dropdown.list.item icon="heroicon-s-printer" wire:click="printAcceptanceLetter({{ $getRecord()->id }})">
                       Print
                    </x-filament::dropdown.list.item>
                    <x-filament::dropdown.list.item icon="heroicon-s-document-arrow-down" wire:click="downloadAcceptanceLetter({{ $getRecord()->id }})">
                        Download
                    </x-filament::dropdown.list.item>
                </x-filament::dropdown.list>
            </x-filament::dropdown>
        </div>

        {{-- FEE SCHEDULE --}}  
        <div class="flex justify-center gap-4" style="margin-top: 1rem">
            <x-filament::dropdown>
                <x-slot name="trigger">
                    <x-filament::button tooltip="Export fee schedule">
                        Fee schedule
                    </x-filament::button>
                </x-slot>
                <x-filament::dropdown.list>
                    @if($admissionSettings->fee_schedule)
                    <x-filament::dropdown.list.item 
                        icon="heroicon-s-document-arrow-down"
                        tag="a"
                        href="{{ Storage::url($admissionSettings->fee_schedule) }}"
                        target="_blank"
                        download="{{ basename($admissionSettings->fee_schedule) }}">
                        Download
                    </x-filament::dropdown.list.item>
                    @else
                    <x-filament::dropdown.list.item disabled>
                        No file available
                    </x-filament::dropdown.list.item>
                @endif
                </x-filament::dropdown.list>
            </x-filament::dropdown>
        </div>

    </div>

    @elseif ($getRecord()->application->admission_status === AdmissionStatus::DENIED)
    <div class="flex-1 bg-gray-50 rounded-sm shadow-sm border border-gray-200 p-4">
        <div class=" flex items-center justify-center pb-2">
            <x-heroicon-s-exclamation-circle :style="'stroke: rgb(222, 2, 2)'" class="w-6 h-6" />
        </div>
        <h2 class="font-bold text-center text-gray-700"> YOUR ADMISSION IS DENIED
        </h2>
        <p class="text-gray-900 text-center text-sm">Dear applicant, we regret to inform you that your admission has
            been denied.<br>Thank you for your interest, and we wish you the best in your future endeavors. </p>
    </div>
    @endif
    <div>

    </div>

</div>
