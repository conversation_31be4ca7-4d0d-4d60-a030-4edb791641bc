<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;


enum WaecSubject: string implements <PERSON><PERSON><PERSON><PERSON>
{
    case ACCOUNTING = 'accounting';
    case AGRICULTURAL_SCIENCE = 'agricultural_science';
    case ARABIC = 'arabic';
    case BIOLOGY = 'biology';
    case BOOK_KEEPING = 'book_keeping';
    case CHEMISTRY = 'chemistry';
    case CHRISTIAN_RELIGIOUS_STUDIES = 'christian_religious_studies';
    case CIVIC_EDUCATION = 'civic_education';
    case COMMERCE = 'commerce';
    case COMPUTER_STUDIES = 'computer_studies';
    case DATA_PROCESSING = 'data_processing';
    case ECONOMICS = 'economics';
    case ENGLISH_LANGUAGE = 'english_language';
    case FINANCIAL_ACCOUNTING = 'financial_accounting';
    case FISHERIES = 'fisheries';
    case FRENCH = 'french';
    case FURTHER_MATHEMATICS = 'further_mathematics';
    case GEOGRAPHY = 'geography';
    case GOVERNMENT = 'government';
    case HAUSA = 'hausa';
    case HEALTH_EDUCATION = 'health_education';
    case HISTORY = 'history';
    case HOME_MANAGEMENT = 'home_management';
    case IGBO = 'igbo';
    case INFORMATION_TECHNOLOGY = 'information_technology';
    case ISLAMIC_RELIGIOUS_STUDIES = 'islamic_religious_studies';
    case LITERATURE_IN_ENGLISH = 'literature_in_english';
    case MATHEMATICS = 'mathematics';
    case MUSIC = 'music';
    case PHYSICAL_EDUCATION = 'physical_education';
    case PHYSICS = 'physics';
    case TECHNICAL_DRAWING = 'technical_drawing';
    case VISUAL_ARTS = 'visual_arts';
    case YORUBA = 'yoruba';

    /**
     * Get the label for the enum
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::ACCOUNTING => 'Accounting',
            self::AGRICULTURAL_SCIENCE => 'Agricultural Science',
            self::ARABIC => 'Arabic',
            self::BIOLOGY => 'Biology',
            self::BOOK_KEEPING => 'Book Keeping',
            self::CHEMISTRY => 'Chemistry',
            self::CHRISTIAN_RELIGIOUS_STUDIES => 'Christian Religious Studies',
            self::CIVIC_EDUCATION => 'Civic Education',
            self::COMMERCE => 'Commerce',
            self::COMPUTER_STUDIES => 'Computer Studies',
            self::DATA_PROCESSING => 'Data Processing',
            self::ECONOMICS => 'Economics',
            self::ENGLISH_LANGUAGE => 'English Language',
            self::FINANCIAL_ACCOUNTING => 'Financial Accounting',
            self::FISHERIES => 'Fisheries',
            self::FRENCH => 'French',
            self::FURTHER_MATHEMATICS => 'Further Mathematics',
            self::GEOGRAPHY => 'Geography',
            self::GOVERNMENT => 'Government',
            self::HAUSA => 'Hausa',
            self::HEALTH_EDUCATION => 'Health Education',
            self::HISTORY => 'History',
            self::HOME_MANAGEMENT => 'Home Management',
            self::IGBO => 'Igbo',
            self::INFORMATION_TECHNOLOGY => 'Information and Communication Technology',
            self::ISLAMIC_RELIGIOUS_STUDIES => 'Islamic Religious Studies',
            self::LITERATURE_IN_ENGLISH => 'Literature in English',
            self::MATHEMATICS => 'Mathematics',
            self::MUSIC => 'Music',
            self::PHYSICAL_EDUCATION => 'Physical Education',
            self::PHYSICS => 'Physics',
            self::TECHNICAL_DRAWING => 'Technical Drawing',
            self::VISUAL_ARTS => 'Visual Arts',
            self::YORUBA => 'Yoruba',
        };
    }
}
