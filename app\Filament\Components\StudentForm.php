<?php

namespace App\Filament\Components;

use App\Enums\Title;
use App\Models\Level;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Enums\ExamBoard;
use App\Enums\WaecGrade;
use App\Models\Semester;
use App\Models\Programme;
use App\Enums\WaecSubject;
use App\Enums\AddressState;
use App\Models\SchoolSession;
use App\Enums\AdmissionStatus;
use App\Enums\GuardianOccupation;
use Illuminate\Support\HtmlString;
use App\Enums\GuardianRelationship;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Illuminate\Database\Eloquent\Builder;

class StudentForm
{
    public static function schema(): array
    {
        return [
            Section::make('A. Login Details')
                ->description(fn(string $operation) => $operation === 'view' ?
                    "The student's login information."
                    : "Kindly provide the applicant's login information.")
                ->schema([
                    Grid::make(['default' => 1, 'md' => 2, 'lg' => 3])
                        ->schema([
                            TextInput::make('phone')
                                ->required()
                                ->unique(ignoreRecord: true)
                                ->tel()
                                ->autocomplete('tel')
                                ->maxLength(11)
                                ->autofocus(fn(string $operation) => $operation === 'create')
                                ->prefixIcon('heroicon-m-phone')
                                ->placeholder(fn($operation) => $operation === 'view' ? null : '07042366520'),
                            TextInput::make('password')
                                ->disabledOn(['edit', 'view'])
                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Kindly set a simple password that is easy to remember.')
                                ->revealable()
                                ->confirmed()
                                ->password()
                                ->minLength(4)
                                ->required(fn(string $operation) => $operation === 'create'),
                            TextInput::make('password_confirmation')
                                ->disabledOn(['edit', 'view'])
                                ->dehydrated(false)
                                ->revealable()
                                ->password()
                                ->required(fn(string $operation) => $operation === 'create')
                        ])
                ]),
            Section::make('B. Personal Details')
                ->description(fn(string $operation) => $operation === 'view' ?
                    "The student's personal information."
                    : "Kindly provide the applicant's personal information.")
                ->schema([
                    Grid::make(['default' => 1, 'md' => 2, 'lg' => 3])
                        ->schema([
                            TextInput::make('last_name')
                                ->required()
                                ->maxLength(20)
                                ->label('Surname')
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Hamzat'),
                            TextInput::make('first_name')
                                ->required()
                                ->maxLength(20)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Mikail'),
                            TextInput::make('middle_name')
                                ->maxLength(20)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Olatunji'),
                            DatePicker::make('date_of_birth')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->label('Date of birth')
                                ->prefixIcon('heroicon-m-calendar-days')
                                ->closeOnDateSelection()
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Pick a date')
                                ->minDate(now()->subYears(50))
                                ->maxDate(now())
                                ->native(false),
                            Select::make('gender')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->options([
                                    'male' => 'Male',
                                    'female' => 'Female',
                                ])
                                ->native(false),
                            Select::make('marital_status')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->options([
                                    'single' => 'Single',
                                    'married' => 'Married',
                                    'divorced' => 'Divorced',
                                    'widowed' => 'Widowed',
                                ])
                                ->native(false),
                            Select::make('religion')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->options([
                                    'christianity' => 'Christianity',
                                    'islam' => 'Islam',
                                    'other' => 'Other',
                                ])
                                ->native(false),
                            Select::make('nationality')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->options([
                                    'nigerian' => 'Nigerian',
                                    'foreigner' => 'Foreigner',
                                ])
                                ->native(false),
                            TextInput::make('email')
                                ->required()
                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Your email is needed for online payment and password reset.')
                                ->email()
                                ->unique(ignoreRecord: true)
                                ->rules(['email:rfc,dns'])
                                ->placeholder(fn($operation) => $operation === 'view' ? null : '<EMAIL>'),
                            TextInput::make('address_line')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->maxLength(30)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'No. 1 Osolo Way'),
                            TextInput::make('address_town')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->label('Town')
                                ->maxLength(15)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Osogbo'),
                            Select::make('address_state')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->label('State')
                                ->searchable()
                                ->preload()
                                ->options(AddressState::class)
                                ->native(false),
                            Select::make('state_id')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->label('State of origin')
                                ->live()
                                ->searchable()
                                ->preload()
                                ->relationship(
                                    'state',
                                    'name'
                                )
                                ->afterStateUpdated(fn(Set $set) => $set('local_government_area_id', null)),
                            Select::make('local_government_area_id')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->label('Local Government Area')
                                ->live()
                                ->placeholder(fn(Get $get): string => empty($get('state_id')) ? 'Select a state first' : 'Select an LGA')
                                ->relationship(
                                    'localGovernmentArea',
                                    'name',
                                    modifyQueryUsing: fn(Builder $query, Get $get) => $query
                                        ->where('state_id', $get('state_id'))
                                ),
                            FileUpload::make('photo')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Photo size must be between 5kb-200kb.')
                                ->label('Passport photo')
                                ->directory('photos')
                                ->image()
                                ->imageCropAspectRatio('1:1')
                                ->minSize(5)
                                ->maxSize(200)
                                ->uploadingMessage('Uploading passport photo...'),

                        ])
                ]),

            Section::make('C. Guardian Details')
                ->description(fn(string $operation) => $operation === 'view' ?
                    "The student's guardian information."
                    : "Kindly provide the applicant's guardian information.")
                ->schema([
                    Grid::make(['default' => 1, 'md' => 2, 'lg' => 3])
                        ->schema([
                            Select::make('guardian.title')
                                ->options(Title::class)
                                ->native(false)
                                ->required(fn(string $operation) => $operation !== 'edit'),
                            TextInput::make('guardian.last_name')
                                ->maxLength(20)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Omotosho')
                                ->required(fn(string $operation) => $operation !== 'edit'),
                            TextInput::make('guardian.first_name')
                                ->maxLength(20)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Hamzat')
                                ->required(fn(string $operation) => $operation !== 'edit'),
                            Select::make('guardian.relationship')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->options(GuardianRelationship::class)
                                ->native(false),
                            Select::make('guardian.occupation')
                                ->options(GuardianOccupation::class)
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->native(false),
                            TextInput::make('guardian.phone')
                                ->tel()
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->maxLength(11)
                                ->prefixIcon('heroicon-m-phone')
                                ->placeholder(fn($operation) => $operation === 'view' ? null : '07042366520'),
                        ])
                ]),
            Section::make('D. Educational History')
                ->description(fn(string $operation) => $operation === 'view' ?
                    "The student's academic background."
                    : "Kindly provide the applicant's academic background.")
                ->schema([
                    Grid::make(['default' => 1, 'md' => 2, 'lg' => 3])
                        ->schema([
                            TextInput::make('application.secondary_school_attended')
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Akinorun High School Osogbo, Osun State.')
                                ->required(fn(string $operation) => $operation !== 'edit'),
                            Select::make('application.secondary_school_graduation_year')
                                ->label('Graduation year')
                                ->options(collect(range(now()->year, now()->subYears(20)->year))->mapWithKeys(fn($y) => [$y => $y])->toArray())
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Select a year')
                                ->prefixIcon('heroicon-m-calendar-days')
                                ->native(false),
                            TextInput::make('application.jamb_registration_number')
                                ->label('JAMB reg. No. (if applicable)')
                                ->maxLength(10)
                                ->minLength(10)
                                ->placeholder(fn($operation) => $operation === 'view' ? null : '1234567890'),
                            Select::make('application.exam_board')
                                ->label('Exam board')
                                ->options(ExamBoard::class)
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->native(false),
                            Repeater::make('application.exam_result')
                                ->label('Exam results')
                                ->schema([
                                    Grid::make(['default' => 2, 'md' => 2])
                                        ->schema([
                                            Select::make('subject')
                                                ->required(fn(string $operation) => $operation !== 'edit')
                                                ->validationAttribute('subject')
                                                ->label(false)
                                                ->native(false)
                                                ->options(WaecSubject::class)
                                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Select a subject')
                                                ->disableOptionsWhenSelectedInSiblingRepeaterItems(),
                                            Select::make('grade')
                                                ->required(fn(string $operation) => $operation !== 'edit')
                                                ->validationAttribute('grade')
                                                ->label(false)
                                                ->placeholder(fn($operation) => $operation === 'view' ? null : 'Select a grade')
                                                ->options(WaecGrade::class)
                                                ->native(false),
                                        ])
                                ])
                                ->grid(2)
                                ->columns(4)
                                ->columnSpanFull()
                                ->defaultItems(7)
                                ->minItems(fn($operation) => $operation === 'create' ? 7 : 0)
                                ->maxItems(9)
                                ->reorderable(false)
                        ])
                ]),
            Section::make('E. Application Details')
                ->description("Kindly provide the applicant's application information.")
                ->hidden(fn($record) => $record ? $record->application?->admission_status === AdmissionStatus::APPROVED : false)
                ->schema([
                    Grid::make(['default' => 1, 'md' => 2, 'lg' => 3])
                        ->schema([
                            Select::make('application.school_session_id')
                                ->label('Entry session ')
                                ->options(SchoolSession::orderByDesc('name')->pluck('name', 'id'))
                                ->default(activeSchoolSession()?->id)
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->native(false),
                            Select::make('application.programme_id')
                                ->label('Entry programme')
                                ->hintIcon('heroicon-m-question-mark-circle', tooltip: 'Programme is the subject combination assigned on admission. It may change after screening or interview.')
                                ->options(Programme::where('is_active', true)->pluck('name', 'id'))
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->native(false),
                            Checkbox::make('application.is_declared')
                                ->required(fn(string $operation) => $operation !== 'edit')
                                ->label('I declare that the information provided is true and correct.')
                                ->inline(false)
                                ->accepted()
                                ->columnSpanFull()
                        ])
                ]),
            Section::make('E. Academic Details')
                ->description(new HtmlString("The student's school academic information."))
                ->visible(fn($record) => $record ? $record->application?->admission_status === AdmissionStatus::APPROVED : false)
                ->schema([
                    Grid::make(['default' => 1, 'md' => 2, 'lg' => 3])
                        ->schema([
                            TextInput::make('matric_number')
                                ->label('Matric. no.')
                                ->disabled(),
                            Select::make('activeRegistration.school_session_id')
                                ->label('Session')
                                ->options(SchoolSession::pluck('name', 'id'))
                                ->default(fn($record) => $record?->activeRegistration?->school_session_id)
                                ->disabled(),
                            Select::make('activeRegistration.semester_id')
                                ->label('Semester')
                                ->options(Semester::pluck('name', 'id'))
                                ->default(fn($record) => $record?->activeRegistration?->semester_id)
                                ->disabled(),
                            Select::make('activeRegistration.programme_id')
                                ->label('Programme')
                                ->options(Programme::pluck('name', 'id'))
                                ->default(fn($record) => $record?->activeRegistration?->programme_id)
                                ->disabled(),
                            Select::make('activeRegistration.level_id')
                                ->label('Level')
                                ->options(Level::pluck('name', 'id'))
                                ->default(fn($record) => $record?->activeRegistration?->level_id)
                                ->disabled(),
                        ])
                ]),

        ];
    }
}
