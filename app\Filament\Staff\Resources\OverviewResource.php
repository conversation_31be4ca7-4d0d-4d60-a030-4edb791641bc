<?php

namespace App\Filament\Staff\Resources;

use App\Enums\Role;
use App\Models\User;
use App\Models\Grade;
use App\Models\Level;
use App\Models\Course;
use App\Models\Semester;
use App\Models\Department;
use App\Models\ScoreSheet;
use App\Models\TotalScore;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use App\Models\Registration;
use App\Models\SchoolSession;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Columns\ColumnGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use App\Filament\Staff\Resources\OverviewResource\Pages;


class OverviewResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?int $navigationSort = 3;
    protected static ?string $modelLabel = 'Overview';
    protected static ?string $navigationGroup = 'Result';

    public static function canAccess(): bool
    {
        return management_staff_access() || Auth::user()?->role === Role::HOD;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('role', Role::STUDENT)
            ->with(['registrations']);
    }

    public static function table(Table $table): Table
    {
        $livewire = $table->getLivewire();

        return $table
            ->deferLoading()
            ->deferFilters()
            // ->persistFiltersInSession()
            ->paginated([10, 20, 30, 40, 50])
            ->striped()
            ->recordAction(null)
            ->defaultSort('last_name')
            ->emptyStateHeading(fn(HasTable $livewire) => new HtmlString(self::getEmptyStateHeading($livewire)))
            ->emptyStateDescription(fn(HasTable $livewire) => new HtmlString(self::getEmptyStateDescription($livewire)))
            ->description("Overview provides a comprehensive summary of students’ academic performance.")
            ->searchPlaceholder('Search (student name)')

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('name')
                    ->sortable(
                        query: fn($query, $direction) =>
                        $query->orderBy("users.last_name", $direction)
                    )
                    ->searchable(
                        query: fn($query, $search) =>
                        $query->whereRaw("CONCAT(users.last_name, ' ', users.first_name, ' ', users.middle_name) LIKE ?", ["%{$search}%"])
                    ),
                TextColumn::make('matric_number')
                    ->label('Matric no.'),
                ColumnGroup::make('Semester summary')
                    ->label(function (HasTable $livewire) {
                        $courses = self::getCourses($livewire);
                        $totalCreditUnit = self::getSemesterTotalCreditUnit($courses) ?? 0;
                        return 'Semester summary (Total Credit Unit: ' . $totalCreditUnit . ')';
                    })
                    ->columns([
                        TextColumn::make('gpa')
                            ->label(new HtmlString("<div x-tooltip=\"'Grade Point Average'\">GPA</div>"))
                            ->state(function ($record, $livewire) {
                                $courses = self::getCourses($livewire);
                                $totalCreditUnit = self::getSemesterTotalCreditUnit($courses) ?? 0;
                                $gradePointAverage = $totalCreditUnit > 0  ? number_format(self::getSemesterTotalGradePoint($livewire, $record, $courses) / $totalCreditUnit, 2) : 0;
                                $record->calculatedGradePointAverage = $gradePointAverage;
                                return $gradePointAverage;
                            }),
                        TextColumn::make('remark')
                            ->state(fn($record) => self::getRemarkFromGradePointAverage($record->calculatedGradePointAverage)?->remark),
                        TextColumn::make('outstanding')
                            ->words(4)
                            ->tooltip(function ($state): ?string {
                                // Count course codes by splitting on comma
                                $courseCount = count(array_filter(explode(',', $state)));

                                if ($courseCount <= 2) { // Adjust this number based on how many courses you want to show
                                    return null;
                                }

                                return $state;
                            })

                            ->label(new HtmlString("<div x-tooltip=\"'Carry-over courses'\">Outstanding</div>"))
                            ->state(fn($record, $livewire) => self::getSemesterOutstandingCourses($record, $livewire)->pluck('code')->implode(', ')),
                    ])->alignment(Alignment::Center),
            ])

            ->filters([
                SelectFilter::make('overview_filter')
                    ->form([
                        Select::make('school_session_id')
                            ->required()
                            ->label('Session')
                            ->native(false)
                            ->options(function () {
                                return SchoolSession::query()
                                    ->orderBy('name', 'desc')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSchoolSession()->id),
                        Select::make('semester_id')
                            ->required()
                            ->label('Semester')
                            ->native(false)
                            ->options(function () {
                                return Semester::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            })
                            ->default(activeSemester()?->id),
                        Select::make('level_id')
                            ->required()
                            ->label('Level')
                            ->native(false)
                            ->options(function () {
                                return Level::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            }),
                        Select::make('department_id')
                            ->required()
                            ->label('Department')
                            ->native(false)
                            ->searchable()
                            ->options(function () {
                                return Department::query()
                                    ->orderBy('name')
                                    ->pluck('name', 'id');
                            }),
                    ])
                    ->columns(4)
                    ->baseQuery(function (Builder $query, HasTable $livewire): Builder {
                        if (!self::hasRequiredFilters($livewire) || !self::isScoreSheetAvailable($livewire)) {
                            return $query->whereRaw('1 = 0');
                        }

                        return $query;
                    })
                    ->query(function (Builder $query, array $data) {
                        $department = Department::find($data['department_id']);

                        return $query->whereHas('registrations', function ($q) use ($data, $department) {
                            $q->where('school_session_id', $data['school_session_id'])
                                ->where('semester_id', $data['semester_id'])
                                ->where('level_id', $data['level_id']);

                            if (!($department?->is_edu || $department?->is_gse)) {
                                $q->where(function ($q) use ($data) {
                                    $q->whereHas('programme', function ($q) use ($data) {
                                        $q->where('first_department_id', $data['department_id'])
                                            ->orWhere('second_department_id', $data['department_id']);
                                    });
                                });
                            }
                        });
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($sessionId = $data['school_session_id'] ?? null) {
                            if ($name = SchoolSession::find($sessionId)?->name) {
                                $indicators[] = Indicator::make("Session: {$name}")->removable(false);
                            }
                        }

                        if ($semesterId = $data['semester_id'] ?? null) {
                            if ($name = Semester::find($semesterId)?->name) {
                                $indicators[] = Indicator::make("Semester: {$name}")->removable(false);
                            }
                        }

                        if ($levelId = $data['level_id'] ?? null) {
                            if ($name = Level::find($levelId)?->name) {
                                $indicators[] = Indicator::make("Level: {$name}")->removable(false);
                            }
                        }

                        if ($deptId = $data['department_id'] ?? null) {
                            if ($name = Department::find($deptId)?->name) {
                                $indicators[] = Indicator::make("Department: {$name}")->removable(false);
                            }
                        }

                        return $indicators;
                    })

            ], layout: FiltersLayout::AboveContent)
            ->filtersFormColumns(1)
            ->filtersApplyAction(
                fn(Action $action) => $action->label('View overview'),
            );
    }

    public static function getCourses(HasTable $livewire)
    {
        $filters = self::extractFilters($livewire);

        return Course::select('id', 'code', 'title', 'credit', 'course_status')
            ->where('department_id', $filters['department_id'])
            ->where('level_id', $filters['level_id'])
            ->where('semester_id', $filters['semester_id'])
            ->orderBy('code')
            ->get();
    }

    public static function getCourseColumns(HasTable $livewire): array
    {

        $courses = self::getCourses($livewire);

        return $courses
            ->map(fn($course) => self::createCourseColumn($course))
            ->toArray();
    }

    private static function createCourseColumn($course): ColumnGroup
    {
        $failedScore = self::getFailedScore();

        return ColumnGroup::make("title.{$course->id}")
            ->label(new HtmlString("<div x-tooltip=\"'{$course->title}'\">{$course->code} <span class='text-gray-400'>{$course->credit}{$course->course_status->value}</span></div>"))
            ->columns([
                TextColumn::make("score.{$course->id}")
                    ->label(new HtmlString("<div x-tooltip=\"'Score'\"> S </div>"))
                    ->color(fn($state): string => ($state <= $failedScore) ? 'danger' : 'black')
                    ->state(function ($record, $livewire) use ($course) {
                        $courseData = self::getCourseData($livewire, $record, $course);
                        $record->courseData = $courseData;
                        return $courseData['score'] ?? '-';
                    }),
                TextColumn::make("grade.{$course->id}")
                    ->label(new HtmlString("<div x-tooltip=\"'Grade'\">G</div>"))
                    ->color(fn($record): string => ($record->courseData['score'] <= $failedScore) ? 'danger' : 'black')
                    ->state(fn($record) => $record->courseData['grade'] ?? '-'),
                TextColumn::make("point.{$course->id}")
                    ->label(new HtmlString("<div x-tooltip=\"'Point'\">P</div>"))
                    ->state(fn($record) => $record->courseData['point'] ?? '-'),
                TextColumn::make("grade_point.{$course->id}")
                    ->label(new HtmlString("<div x-tooltip=\"'Grade Point'\">GP</div>"))
                    ->state(fn($record) => $record->courseData['grade_point'] ?? '-'),
            ])
            ->alignment(Alignment::Center);
    }

    private static function getCourseData(HasTable $livewire, $record, $course)
    {
        $filters = self::extractFilters($livewire);
        $registration = self::getRegistration($record->id, $filters);

        if (!$registration) {
            return null;
        }

        $totalScore = TotalScore::where([
            'registration_id' => $registration->id,
            'course_id' => $course->id,
        ])->value('total');

        $grade = self::getGradeFromScore($totalScore);

        return [
            'score' => $totalScore,
            'grade' => $grade?->name,
            'point' => $grade?->point,
            'grade_point' => $grade ? $grade->point * $course->credit : null,
        ];
    }

    public static function getSemesterTotalCreditUnit($courses)
    {
        return $courses->sum('credit');
    }

    public static function getSemesterTotalGradePoint(HasTable $livewire, $record, $courses)
    {
        return $courses->map(fn($course) => self::getCourseData($livewire, $record, $course)['grade_point'])->sum();
    }

    public static function getSemesterOutstandingCourses(User $record, HasTable $livewire)
    {
        $failedScore = self::getFailedScore();
        $filters = self::extractFilters($livewire);
        $registrations = $record->registrations;
        $levelIds = $registrations->pluck('level_id')->unique();
        $semesterIds = $registrations->pluck('semester_id')->unique();
        $courses = Course::select('id', 'code')
            ->whereIn('level_id', $levelIds)
            ->whereIn('semester_id', $semesterIds)
            ->where('department_id', $filters['department_id'])
            ->get();
        $failedCourses = $courses->filter(function ($course) use ($record, $filters, $failedScore) {
            $totalScore = TotalScore::where([
                'registration_id' => self::getRegistration($record->id, $filters)->id,
                'course_id' => $course->id,
            ])->value('total');

            return $totalScore <= $failedScore;
        });

        return $failedCourses;
    }

    public static function getSemesterGradePoint(Registration $registration, Course $course, Collection $grades): ?float
    {
        $totalScore = TotalScore::where([
            'registration_id' => $registration->id,
            'course_id' => $course->id,
        ])->value('total');

        $grade = self::getGradeFromScore($totalScore, $grades);
        return $grade ? (float) ($grade->point * $course->credit) : null;
    }

    public static function getGradeFromScore(?int $totalScore, ?Collection $grades = null)
    {
        if ($totalScore === null) {
            return null;
        }

        static $grades;
        $grades ??= Grade::all();
        return $grades->first(
            fn($grade) => $totalScore >= $grade->min_score && $totalScore <= $grade->max_score
        );
    }

    public static function getRemarkFromGradePointAverage(?float $gradePointAverage, ?Collection $grades = null)
    {
        if ($gradePointAverage === null) {
            return null;
        }

        static $grades;
        $grades ??= Grade::all();

        return $grades->first(
            fn($grade) => $gradePointAverage >= $grade->min_point && $gradePointAverage <= $grade->max_point
        );
    }

    private static function getRegistration(int $userId, array $filters): ?Registration
    {
        return Registration::where([
            'user_id' => $userId,
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'level_id' => $filters['level_id'],
        ])->first();
    }

    public static function getFailedScore(): int
    {
        static $failedScore;
        $failedScore ??= Grade::where('min_score', 0)->value('max_score');
        return $failedScore;
    }

    private static function isScoreSheetPublishedForRegistration(Registration $registration, ?int $departmentId): bool
    {
        if (!$departmentId) {
            return false;
        }
        return ScoreSheet::where('school_session_id', $registration->school_session_id)
            ->where('semester_id', $registration->semester_id)
            ->where('department_id', $departmentId)
            ->where('is_published', true)
            ->exists();
    }

    private static function isScoreSheetAvailable(HasTable $livewire): bool
    {
        return self::isScoreSheetCreated($livewire) && self::isScoreSheetPublished($livewire);
    }

    public static function isScoreSheetCreated(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return ScoreSheet::where([
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
        ])->exists();
    }

    public static function isScoreSheetPublished(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return ScoreSheet::where([
            'school_session_id' => $filters['school_session_id'],
            'semester_id' => $filters['semester_id'],
            'department_id' => $filters['department_id'],
            'is_published' => true,
        ])->exists();
    }

    private static function getEmptyStateHeading(HasTable $livewire): string
    {
        if (!self::hasRequiredFilters($livewire)) {
            return 'Options are Required to View Overview';
        }

        if (!self::isScoreSheetCreated($livewire)) {
            return 'Score-Sheet Has Not Been Created Yet';
        }

        if (!self::isScoreSheetPublished($livewire)) {
            return 'Score-Sheet Has Not Been Published Yet';
        }

        return 'No Overview Found';
    }

    private static function getEmptyStateDescription(HasTable $livewire): string
    {
        if (!self::hasRequiredFilters($livewire)) {
            return 'Select <b>session</b>, <b>semester</b>, <b>level</b>, and <b>department</b> to view the overview.';
        }

        if (!self::isScoreSheetCreated($livewire)) {
            return 'The score-sheet for this session, semester, and department has not been created yet. <b>Contact your admin</b> for more information.';
        }

        if (!self::isScoreSheetPublished($livewire)) {
            return 'The score-sheet for this session, semester, and department has not been published yet. <b>Contact your admin</b> for more information.';
        }

        return 'No overview found for the selected options.';
    }

    public static function extractFilters(HasTable $livewire): array
    {

        $filters = $livewire->tableFilters['overview_filter'] ?? [];

        return [
            'school_session_id' => $filters['school_session_id'] ?? null,
            'semester_id' => $filters['semester_id'] ?? null,
            'level_id' => $filters['level_id'] ?? null,
            'department_id' => $filters['department_id'] ?? null,
        ];
    }

    private static function hasRequiredFilters(HasTable $livewire): bool
    {
        $filters = self::extractFilters($livewire);

        return !empty($filters['school_session_id'])
            && !empty($filters['semester_id'])
            && !empty($filters['level_id'])
            && !empty($filters['department_id']);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageOverview::route('/'),
        ];
    }
}
