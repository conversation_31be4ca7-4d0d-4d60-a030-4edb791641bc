<?php

namespace App\Filament\Staff\Clusters\Scores\Resources\ScoreResource\Pages;

use App\Filament\Staff\Clusters\Scores\Resources\ScoreResource;
use Filament\Resources\Pages\ManageRecords;

class ManageScores extends ManageRecords
{
    protected static string $resource = ScoreResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
