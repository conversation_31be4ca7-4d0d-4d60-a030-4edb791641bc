<?php

namespace App\Filament\Staff\Resources\StudentResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Staff\Resources\StudentResource;


class ViewStudent extends ViewRecord
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['guardian']['title'] = $this->record->guardian?->title ?? null;
        $data['guardian']['relationship'] = $this->record->guardian?->relationship ?? null;
        $data['guardian']['occupation'] = $this->record->guardian?->occupation ?? null;
        $data['guardian']['phone'] = $this->record->guardian?->phone ?? null;
        $data['guardian']['first_name'] = $this->record->guardian?->first_name ?? null;
        $data['guardian']['last_name'] = $this->record->guardian?->last_name ?? null;
        $data['application']['secondary_school_attended'] = $this->record->application?->secondary_school_attended ?? null;
        $data['application']['secondary_school_graduation_year'] = $this->record->application?->secondary_school_graduation_year ?? null;
        $data['application']['jamb_registration_number'] = $this->record->application?->jamb_registration_number ?? null;
        $data['application']['exam_board'] = $this->record->application?->exam_board ?? null;
        $data['application']['programme_id'] = $this->record->application?->programme?->id ?? null;
        $data['application']['school_session_id'] = $this->record->application?->schoolSession?->id ?? null;
        $data['application']['is_declared'] = $this->record->application?->is_declared ?? null;
        $data['application']['exam_result'] = $this->record->application?->exam_result ?? [];
        $data['activeRegistration']['school_session_id'] = $this->record->activeRegistration?->schoolSession?->id ?? null;
        $data['activeRegistration']['semester_id'] = $this->record->activeRegistration?->semester?->id ?? null;
        $data['activeRegistration']['level_id'] = $this->record->activeRegistration?->level?->id ?? null;
        $data['activeRegistration']['programme_id'] = $this->record->activeRegistration?->programme?->id ?? null;

        return $data;
    }
}
