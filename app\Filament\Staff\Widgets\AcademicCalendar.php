<?php

namespace App\Filament\Staff\Widgets;

use Carbon\Carbon;
use App\Models\SchoolSession;
use App\Models\SemesterSchedule;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\IconPosition;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;

class AcademicCalendar extends BaseWidget
{
    protected static ?string $pollingInterval = '60s';
    protected ?string $heading = 'Academic Calendar';
    protected ?string $description = 'Stay updated on key academic dates and resumptions.';
    protected static ?int $sort = 3;

    public static function canView(): bool
    {
        $user = Auth::user();

        return $user && in_array($user->role, all_staff_roles());
    }

    protected function getStats(): array
    {
        return [
            Stat::make('Current Session', $this->getCurrentSession())
                ->description(new HtmlString($this->getCurrentSessionPeriod()))
                ->descriptionIcon('heroicon-m-calendar-days', IconPosition::Before)
                ->color('info'),

            Stat::make('Current Semester', $this->getCurrentSemester())
                ->description(new HtmlString($this->getCurrentSemesterPeriod()))
                ->descriptionIcon('heroicon-m-calendar-days', IconPosition::Before)
                ->color('info'),

            Stat::make('Next Semester', $this->getNextSemester())
                ->description(new HtmlString($this->getNextSemesterPeriod()))
                ->descriptionIcon('heroicon-m-calendar-days', IconPosition::Before)
                ->color('info'),
        ];
    }

    private function getCurrentSession(): string
    {
        return activeSchoolSession()?->name ?? 'No active session';
    }

    private function getCurrentSessionPeriod(): string
    {
        $activeSession = activeSchoolSession();

        if (!$activeSession) {
            return 'No session details';
        }

        $semesters = SemesterSchedule::where('school_session_id', $activeSession->id)
            ->orderBy('semester_start')
            ->get(['semester_start', 'semester_end']);

        if ($semesters->isEmpty()) {
            return 'No session details';
        }

        $start = Carbon::parse($semesters->first()->semester_start)->format('M j, Y');
        $end = Carbon::parse($semesters->last()->semester_end)->format('M j, Y');

        return "<b>{$start}</b> to <b>{$end}</b>";
    }

    private function getCurrentSemester(): string
    {
        return activeSemester()?->name ?? 'Holiday';
    }

    private function getCurrentSemesterPeriod(): string
    {
        $currentSemester = activeSemester();
        $activeSession = activeSchoolSession();

        if (!$currentSemester || !$activeSession) {
            return 'No semester details';
        }

        $schedule = SemesterSchedule::where('school_session_id', $activeSession->id)
            ->where('semester_id', $currentSemester->id)
            ->first(['semester_start', 'semester_end']);

        if (!$schedule) {
            return 'No semester details';
        }

        $start = Carbon::parse($schedule->semester_start)->format('M j, Y');
        $end = Carbon::parse($schedule->semester_end)->format('M j, Y');

        return "<b>{$start}</b> to <b>{$end}</b>";
    }

    private function getNextSemester(): string
    {
        $currentSemester = activeSemester();
        $activeSession = activeSchoolSession();

        if (!$activeSession) {
            return 'No upcoming semester';
        }

        // If no current semester (holiday), get the next semester from current session
        if (!$currentSemester) {
            $nextSemester = SemesterSchedule::where('school_session_id', $activeSession->id)
                ->where('semester_start', '>', now())
                ->orderBy('semester_start')
                ->with('semester')
                ->first();

            return $nextSemester?->semester?->name ?? 'No upcoming semester';
        }

        // Look for next semester in current session
        $nextSemester = SemesterSchedule::where('school_session_id', $activeSession->id)
            ->where('semester_start', '>', now())
            ->orderBy('semester_start')
            ->with('semester')
            ->first();

        if ($nextSemester) {
            return $nextSemester->semester->name;
        }

        // Look for first semester in next session
        $currentSessionEnd = $this->getSessionEndDate($activeSession->id);

        if ($currentSessionEnd) {
            $nextSession = SchoolSession::whereHas('semesterSchedules', function ($query) use ($currentSessionEnd) {
                $query->where('semester_start', '>', $currentSessionEnd);
            })
                ->orderBy('id')
                ->first();

            if ($nextSession) {
                $firstSemesterNextSession = SemesterSchedule::where('school_session_id', $nextSession->id)
                    ->orderBy('semester_start')
                    ->with('semester')
                    ->first();

                return $firstSemesterNextSession?->semester?->name ?? 'No upcoming semester';
            }
        }

        return 'No upcoming semester';
    }

    private function getNextSemesterPeriod(): string
    {
        $currentSemester = activeSemester();
        $activeSession = activeSchoolSession();

        if (!$activeSession) {
            return 'No semester details';
        }

        // If no current semester (holiday), get the next semester from current session
        if (!$currentSemester) {
            $nextSchedule = SemesterSchedule::where('school_session_id', $activeSession->id)
                ->where('semester_start', '>', now())
                ->orderBy('semester_start')
                ->first(['semester_start', 'semester_end']);

            return $this->formatSemesterPeriod($nextSchedule);
        }

        // Look for next semester in current session
        $nextSchedule = SemesterSchedule::where('school_session_id', $activeSession->id)
            ->where('semester_start', '>', now())
            ->orderBy('semester_start')
            ->first(['semester_start', 'semester_end']);

        if ($nextSchedule) {
            return $this->formatSemesterPeriod($nextSchedule);
        }

        // Look for first semester in next session
        $currentSessionEnd = $this->getSessionEndDate($activeSession->id);

        if ($currentSessionEnd) {
            $nextSession = SchoolSession::whereHas('semesterSchedules', function ($query) use ($currentSessionEnd) {
                $query->where('semester_start', '>', $currentSessionEnd);
            })
                ->orderBy('id')
                ->first();

            if ($nextSession) {
                $firstSemesterNextSession = SemesterSchedule::where('school_session_id', $nextSession->id)
                    ->orderBy('semester_start')
                    ->first(['semester_start', 'semester_end']);

                return $this->formatSemesterPeriod($firstSemesterNextSession);
            }
        }

        return 'No semester details';
    }

    private function getSessionEndDate(int $sessionId): ?Carbon
    {
        $lastSemester = SemesterSchedule::where('school_session_id', $sessionId)
            ->orderBy('semester_end', 'desc')
            ->first(['semester_end']);

        return $lastSemester ? Carbon::parse($lastSemester->semester_end) : null;
    }

    private function formatSemesterPeriod(?SemesterSchedule $schedule): string
    {
        if (!$schedule) {
            return 'No semester details';
        }

        $start = Carbon::parse($schedule->semester_start)->format('M j, Y');
        $end = Carbon::parse($schedule->semester_end)->format('M j, Y');

        return "<b>{$start}</b> to <b>{$end}</b>";
    }
}
