<?php

namespace App\Filament\Student\Resources;

use App\Enums\Role;
use App\Models\User;
use Filament\Tables\Table;
use App\Enums\AdmissionStatus;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\Auth;
use Filament\Tables\Columns\Layout\View;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Student\Resources\AdmissionResource\Pages;

class AdmissionResource extends Resource
{
    protected static ?string $model = User::class;
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';
    protected static ?string $pluralModelLabel = 'admission';


    public static function canAccess(): bool
    {
        $user = Auth::user();

        return $user && $user->role === Role::STUDENT
            && $user->application?->admission_status !== null;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['application' => fn($query) => $query->with(['schoolSession', 'programme'])])
            ->where('users.id', Auth::user()->id)
            ->where('users.role', Role::STUDENT)
            ->limit(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->paginated(false)
            ->recordUrl(null)
            ->recordAction(null)
            ->emptyStateHeading('No Admission Yet')
            ->emptyStateDescription("Once you've applied for our school admission, it will appear here.")
            ->description('Your admission details will be displayed here once available.')
            ->columns([
                View::make('filament.tables.admission')
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageAdmission::route('/'),
        ];
    }
}
