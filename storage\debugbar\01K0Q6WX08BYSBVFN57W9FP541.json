{"__meta": {"id": "01K0Q6WX08BYSBVFN57W9FP541", "datetime": "2025-07-21 19:20:47", "utime": **********.368457, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 41, "messages": [{"message": "[19:20:47] LOG.error: Target [Filament\\Tables\\Contracts\\HasTable] is not instantiable while building [Filament\\Tables\\Table]. {\n    \"userId\": 1,\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.050168, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.05823, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.058924, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.059473, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.06001, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.060314, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.060542, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.060759, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.061109, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.293359, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.293642, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.293868, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.294045, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.294263, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.294767, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.295625, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.295931, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.297098, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.297327, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.297539, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.297725, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.297879, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.298021, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.298167, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.298305, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.331138, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.331629, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.332245, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.332512, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.332692, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.332844, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.332998, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.333139, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.357054, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.357241, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.357462, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.357864, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.358809, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.360008, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.360555, "xdebug_link": null, "collector": "log"}, {"message": "[19:20:47] LOG.warning: mb_substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Str.php on line 1701", "message_html": null, "is_string": false, "label": "warning", "time": **********.360866, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.682436, "end": **********.368519, "duration": 0.6860830783843994, "duration_str": "686ms", "measures": [{"label": "Booting", "start": **********.682436, "relative_start": 0, "end": **********.967841, "relative_end": **********.967841, "duration": 0.*****************, "duration_str": "285ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.967858, "relative_start": 0.*****************, "end": **********.368522, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.981975, "relative_start": 0.*****************, "end": **********.983129, "relative_end": **********.983129, "duration": 0.0011539459228515625, "duration_str": "1.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7921040, "peak_usage_str": "8MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Contracts\\Container\\BindingResolutionException", "message": "Target [Filament\\Tables\\Contracts\\HasTable] is not instantiable while building [Filament\\Tables\\Table].", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "line": 1279, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:88</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1026</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">notInstantiable</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Contracts\\HasTable</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>890</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Contracts\\HasTable</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1077</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Contracts\\HasTable</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>821</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Contracts\\HasTable</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1057</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Contracts\\HasTable</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1202</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Contracts\\HasTable</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1101</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">resolveClass</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">[object ReflectionParameter]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1052</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">resolveDependencies</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>ReflectionParameter</span> {<a class=sf-dump-ref>#2249</a><samp data-depth=5 class=sf-dump-compact>\n          +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"8 characters\">livewire</span>\"\n          <span class=sf-dump-meta>position</span>: <span class=sf-dump-num>0</span>\n          <span class=sf-dump-meta>typeHint</span>: \"<span class=\"sf-dump-str sf-dump-ellipsization\" title=\"Filament\\Tables\\Contracts\\HasTable\n34 characters\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">Filament\\Tables\\Contracts</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-class\">\\</span><span class=\"sf-dump-ellipsis-tail\">HasTable</span></span>\"\n        </samp>}\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>890</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">build</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Filament\\Tables\\Table</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1077</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Filament\\Tables\\Table</span>\"\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"63 characters\">vendor/laravel/framework/src/Illuminate/Container/Container.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>821</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">resolve</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Filament\\Tables\\Table</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1057</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\Container</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Filament\\Tables\\Table</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">make</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Filament\\Tables\\Table</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">app/Filament/Staff/Resources/OverviewResource/Pages/ManageOverview.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">app</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Filament\\Tables\\Table</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/filament/tables/src/Concerns/InteractsWithTable.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">makeTable</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"66 characters\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">vendor/filament/support/src/Components/ComponentManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>80</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Concerns\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Filament\\Resources\\Pages\\ListRecords</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/filament/support/src/Concerns/Configurable.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>12</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">configureUsing</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Filament\\Support\\Components\\ComponentManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Actions\\BulkAction</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-const>false</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/filament/tables/src/Concerns/InteractsWithTable.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>52</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">configureUsing</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Filament\\Support\\Components\\Component</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"59 characters\">vendor/filament/support/src/Components/ComponentManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>80</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Filament\\Tables\\Concerns\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Filament\\Resources\\Pages\\ListRecords</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/filament/support/src/Concerns/Configurable.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>12</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">configureUsing</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"44 characters\">Filament\\Support\\Components\\ComponentManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">Filament\\Tables\\Actions\\Action</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-const>false</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/filament/tables/src/Concerns/InteractsWithTable.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">configureUsing</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Filament\\Support\\Components\\Component</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">bootedInteractsWithTable</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Filament\\Resources\\Pages\\ListRecords</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Container/Util.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>96</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">unwrapIfClosure</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Container\\Util</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">callBoundMethod</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a><samp data-depth=5 id=sf-dump-*********-ref22672 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">__id</span>: \"<span class=sf-dump-str title=\"20 characters\">wNSGdxO3rx3uuZuhmtZW</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">__name</span>: \"<span class=sf-dump-str title=\"68 characters\">app.filament.staff.resources.overview-resource.pages.manage-overview</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeCollection</span></span> {<a class=sf-dump-ref>#2573</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:20</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2127</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"33 characters\">getFormComponentFileAttachmentUrl</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22125 title=\"7 occurrences\">#2125</a><samp data-depth=9 id=sf-dump-*********-ref22125 class=sf-dump-compact>\n                  +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">METHOD</span>\"\n                </samp>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"33 characters\">getFormComponentFileAttachmentUrl</span>\"\n              </samp>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2128</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"25 characters\">getFormSelectOptionLabels</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22125 title=\"7 occurrences\">#2125</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"25 characters\">getFormSelectOptionLabels</span>\"\n              </samp>}\n              <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2126</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"24 characters\">getFormSelectOptionLabel</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22125 title=\"7 occurrences\">#2125</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"24 characters\">getFormSelectOptionLabel</span>\"\n              </samp>}\n              <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2124</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormSelectOptions</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22125 title=\"7 occurrences\">#2125</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormSelectOptions</span>\"\n              </samp>}\n              <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2123</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"26 characters\">getFormSelectSearchResults</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22125 title=\"7 occurrences\">#2125</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"26 characters\">getFormSelectSearchResults</span>\"\n              </samp>}\n              <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2122</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22125 title=\"7 occurrences\">#2125</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"20 characters\">getFormUploadedFiles</span>\"\n              </samp>}\n              <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2121</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"12 characters\">_startUpload</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22125 title=\"7 occurrences\">#2125</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"12 characters\">_startUpload</span>\"\n              </samp>}\n              <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2120</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"17 characters\">isTableReordering</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a><samp data-depth=9 id=sf-dump-*********-ref22227 class=sf-dump-compact>\n                  +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"8 characters\">PROPERTY</span>\"\n                </samp>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"17 characters\">isTableReordering</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2225</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"12 characters\">tableFilters</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"12 characters\">tableFilters</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2226</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"13 characters\">tableGrouping</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"13 characters\">tableGrouping</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>10</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2228</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"22 characters\">tableGroupingDirection</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"22 characters\">tableGroupingDirection</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>11</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2229</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"11 characters\">tableSearch</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"11 characters\">tableSearch</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>12</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2230</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"15 characters\">tableSortColumn</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"15 characters\">tableSortColumn</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>13</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2231</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"18 characters\">tableSortDirection</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"18 characters\">tableSortDirection</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>14</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2232</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"9 characters\">activeTab</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"9 characters\">activeTab</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>15</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2233</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"13 characters\">defaultAction</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"13 characters\">defaultAction</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"6 characters\">action</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>16</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2234</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"22 characters\">defaultActionArguments</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"22 characters\">defaultActionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"15 characters\">actionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>17</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2235</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"18 characters\">defaultTableAction</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"18 characters\">defaultTableAction</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"11 characters\">tableAction</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>18</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2236</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"27 characters\">defaultTableActionArguments</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"27 characters\">defaultTableActionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"20 characters\">tableActionArguments</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>19</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Url\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Url</span></span> {<a class=sf-dump-ref>#2237</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"24 characters\">defaultTableActionRecord</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22227 title=\"13 occurrences\">#2227</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"24 characters\">defaultTableActionRecord</span>\"\n                +<span class=sf-dump-public title=\"Public property\">as</span>: \"<span class=sf-dump-str title=\"17 characters\">tableActionRecord</span>\"\n                +<span class=sf-dump-public title=\"Public property\">history</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">keep</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">except</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">nullable</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">withValidatorCallback</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">rulesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">messagesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">validationAttributesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">heading</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">subheading</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">maxContentWidth</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">extraBodyAttributes</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedActionsArguments</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">defaultAction</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultActionArguments</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedActions</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">hasActionsModalRendered</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">componentFileAttachments</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">cachedForms</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hasCachedForms</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">isCachingForms</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hasFormsModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">oldFormState</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsArguments</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedFormComponentActionsComponents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">hasInfolistsModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedInfolists</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsComponent</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">mountedInfolistActionsInfolist</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedSubNavigation</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedHeaderActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">isTableReordering</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">tableFilters</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>overview_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>school_session_id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n            </samp>]\n          </samp>]\n          +<span class=sf-dump-public title=\"Public property\">tableGrouping</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">tableGroupingDirection</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">tableSearch</span>: \"\"\n          +<span class=sf-dump-public title=\"Public property\">tableSortColumn</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">tableSortDirection</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">activeTab</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedTabs</span>: <span class=sf-dump-const title=\"Uninitialized property\">? array</span>\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Filament\\Tables\\Table</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hasTableModalRendered</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">shouldMountInteractsWithTable</span>: <span class=sf-dump-const>false</span>\n          +<span class=sf-dump-public title=\"Public property\">isTableLoaded</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">tableRecordsPerPage</span>: <span class=sf-dump-num>10</span>\n          #<span class=sf-dump-protected title=\"Protected property\">defaultTableRecordsPerPageSelectOption</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">tableColumnSearches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">toggledTableColumns</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedTableActions</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedTableActionsData</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedTableActionsArguments</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedTableActionRecord</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedMountedTableActionRecord</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedMountedTableActionRecordKey</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultTableAction</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultTableActionArguments</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">defaultTableActionRecord</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">selectedTableRecords</span>: []\n          +<span class=sf-dump-public title=\"Public property\">mountedTableBulkAction</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">mountedTableBulkActionData</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">cachedSelectedTableRecords</span>: <span class=sf-dump-const title=\"Uninitialized property\">? Illuminate\\Database\\Eloquent\\Collection|Illuminate\\Support\\Collection</span>\n          +<span class=sf-dump-public title=\"Public property\">tableDeferredFilters</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>overview_filter</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>school_session_id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n              \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n            </samp>]\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">allowsDuplicates</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">cachedTableRecords</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">paginators</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n          </samp>]\n        </samp>}\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"24 characters\">bootedInteractsWithTable</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vendor/livewire/livewire/src/Wrapped.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Filament\\Staff\\Resources\\OverviewResource\\Pages</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ManageOverview</span></span> {<a class=sf-dump-ref href=#sf-dump-*********-ref22672 title=\"22 occurrences\">#2672</a>}\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"24 characters\">bootedInteractsWithTable</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"85 characters\">vendor/livewire/livewire/src/Features/SupportLifecycleHooks/SupportLifecycleHooks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Livewire\\Wrapped</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">bootedInteractsWithTable</span>\"\n      <span class=sf-dump-index>1</span> => []\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"85 characters\">vendor/livewire/livewire/src/Features/SupportLifecycleHooks/SupportLifecycleHooks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>45</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">callTraitHook</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">booted</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"46 characters\">vendor/livewire/livewire/src/ComponentHook.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">hydrate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Livewire\\Features\\SupportLifecycleHooks\\SupportLifecycleHooks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">wNSGdxO3rx3uuZuhmtZW</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"68 characters\">app.filament.staff.resources.overview-resource.pages.manage-overview</span>\"\n        \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">staff/overviews</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n        \"<span class=sf-dump-key>children</span>\" => []\n        \"<span class=sf-dump-key>scripts</span>\" => []\n        \"<span class=sf-dump-key>assets</span>\" => []\n        \"<span class=sf-dump-key>errors</span>\" => []\n        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"54 characters\">vendor/livewire/livewire/src/ComponentHookRegistry.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>54</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">callHydrate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Livewire\\ComponentHook</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">wNSGdxO3rx3uuZuhmtZW</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"68 characters\">app.filament.staff.resources.overview-resource.pages.manage-overview</span>\"\n        \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">staff/overviews</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n        \"<span class=sf-dump-key>children</span>\" => []\n        \"<span class=sf-dump-key>scripts</span>\" => []\n        \"<span class=sf-dump-key>assets</span>\" => []\n        \"<span class=sf-dump-key>errors</span>\" => []\n        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"41 characters\">vendor/livewire/livewire/src/EventBus.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Livewire\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Livewire\\ComponentHookRegistry</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"75 characters\">[object App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">wNSGdxO3rx3uuZuhmtZW</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"68 characters\">app.filament.staff.resources.overview-resource.pages.manage-overview</span>\"\n        \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">staff/overviews</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n        \"<span class=sf-dump-key>children</span>\" => []\n        \"<span class=sf-dump-key>scripts</span>\" => []\n        \"<span class=sf-dump-key>assets</span>\" => []\n        \"<span class=sf-dump-key>errors</span>\" => []\n        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Livewire\\Mechanisms\\HandleComponents\\ComponentContext]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vendor/livewire/livewire/src/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>98</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">trigger</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Livewire\\EventBus</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">hydrate</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"75 characters\">[object App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview]</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">wNSGdxO3rx3uuZuhmtZW</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"68 characters\">app.filament.staff.resources.overview-resource.pages.manage-overview</span>\"\n        \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">staff/overviews</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n        \"<span class=sf-dump-key>children</span>\" => []\n        \"<span class=sf-dump-key>scripts</span>\" => []\n        \"<span class=sf-dump-key>assets</span>\" => []\n        \"<span class=sf-dump-key>errors</span>\" => []\n        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Livewire\\Mechanisms\\HandleComponents\\ComponentContext]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>96</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Livewire\\trigger</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">hydrate</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"75 characters\">[object App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview]</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">wNSGdxO3rx3uuZuhmtZW</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"68 characters\">app.filament.staff.resources.overview-resource.pages.manage-overview</span>\"\n        \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">staff/overviews</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n        \"<span class=sf-dump-key>children</span>\" => []\n        \"<span class=sf-dump-key>scripts</span>\" => []\n        \"<span class=sf-dump-key>assets</span>\" => []\n        \"<span class=sf-dump-key>errors</span>\" => []\n        \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      </samp>]\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Livewire\\Mechanisms\\HandleComponents\\ComponentContext]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">vendor/livewire/livewire/src/LivewireManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>102</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Livewire\\Mechanisms\\HandleComponents\\HandleComponents</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:38</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>isTableReordering</span>\" => <span class=sf-dump-const>false</span>\n          \"<span class=sf-dump-key>tableFilters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>overview_filter</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>school_session_id</span>\" => <span class=sf-dump-num>3</span>\n                  \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                  \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                  \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n                </samp>]\n              </samp>]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableGrouping</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableGroupingDirection</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableSearch</span>\" => \"\"\n          \"<span class=sf-dump-key>tableSortColumn</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableSortDirection</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>activeTab</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>defaultAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>componentFileAttachments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsComponents</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsComponent</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedInfolistActionsInfolist</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>isTableLoaded</span>\" => <span class=sf-dump-const>true</span>\n          \"<span class=sf-dump-key>tableRecordsPerPage</span>\" => <span class=sf-dump-num>10</span>\n          \"<span class=sf-dump-key>tableColumnSearches</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>toggledTableColumns</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>selectedTableRecords</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableBulkAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedTableBulkActionData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableDeferredFilters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>overview_filter</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>school_session_id</span>\" => <span class=sf-dump-num>3</span>\n                  \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                  \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                  \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n                </samp>]\n              </samp>]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>memo</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">wNSGdxO3rx3uuZuhmtZW</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"68 characters\">app.filament.staff.resources.overview-resource.pages.manage-overview</span>\"\n          \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">staff/overviews</span>\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          \"<span class=sf-dump-key>children</span>\" => []\n          \"<span class=sf-dump-key>scripts</span>\" => []\n          \"<span class=sf-dump-key>assets</span>\" => []\n          \"<span class=sf-dump-key>errors</span>\" => []\n          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">29c1898d4e899b41f52c9ac11bf4e82e04ec9fa992bef8b86e3cd36ac736f3ae</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"21 characters\">resetTableFiltersForm</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"73 characters\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>94</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Livewire\\LivewireManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:38</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>isTableReordering</span>\" => <span class=sf-dump-const>false</span>\n          \"<span class=sf-dump-key>tableFilters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>overview_filter</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>school_session_id</span>\" => <span class=sf-dump-num>3</span>\n                  \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                  \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                  \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n                </samp>]\n              </samp>]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableGrouping</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableGroupingDirection</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableSearch</span>\" => \"\"\n          \"<span class=sf-dump-key>tableSortColumn</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>tableSortDirection</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>activeTab</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>defaultAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>componentFileAttachments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedFormComponentActionsComponents</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedInfolistActionsComponent</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedInfolistActionsInfolist</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>isTableLoaded</span>\" => <span class=sf-dump-const>true</span>\n          \"<span class=sf-dump-key>tableRecordsPerPage</span>\" => <span class=sf-dump-num>10</span>\n          \"<span class=sf-dump-key>tableColumnSearches</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>toggledTableColumns</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActions</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionsData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionsArguments</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableActionArguments</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>defaultTableActionRecord</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>selectedTableRecords</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>mountedTableBulkAction</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>mountedTableBulkActionData</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>tableDeferredFilters</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>overview_filter</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>school_session_id</span>\" => <span class=sf-dump-num>3</span>\n                  \"<span class=sf-dump-key>semester_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                  \"<span class=sf-dump-key>level_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n                  \"<span class=sf-dump-key>department_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n                </samp>]\n                <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=9 class=sf-dump-compact>\n                  \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n                </samp>]\n              </samp>]\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>memo</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">wNSGdxO3rx3uuZuhmtZW</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"68 characters\">app.filament.staff.resources.overview-resource.pages.manage-overview</span>\"\n          \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">staff/overviews</span>\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          \"<span class=sf-dump-key>children</span>\" => []\n          \"<span class=sf-dump-key>scripts</span>\" => []\n          \"<span class=sf-dump-key>assets</span>\" => []\n          \"<span class=sf-dump-key>errors</span>\" => []\n          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">29c1898d4e899b41f52c9ac11bf4e82e04ec9fa992bef8b86e3cd36ac736f3ae</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"21 characters\">resetTableFiltersForm</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>46</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">handleUpdate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Livewire\\Mechanisms\\HandleRequests\\HandleRequests</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>265</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"58 characters\">[object Livewire\\Mechanisms\\HandleRequests\\HandleRequests]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">handleUpdate</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>211</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>73</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>74</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>75</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>76</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>77</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>78</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>79</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>80</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>81</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>82</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>83</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>84</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>85</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>86</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>20</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>87</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">C:/Program Files/Herd/resources/app.asar.unpacked/resources/valet/server.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>139</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">C:\\Users\\<USER>\\Herd\\racoed\\public\\index.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            $message = \"Target [$concrete] is not instantiable.\";\n", "        }\n", "\n", "        throw new BindingResolutionException($message);\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FContainer%2FContainer.php&line=1279", "ajax": false, "filename": "Container.php", "line": "1279"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 2, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.020810000000000002, "accumulated_duration_str": "20.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.991895, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = '9c1gS32R8L2GpH0C1oPQMYHGdlBbIVpLI4Vrqk4x' limit 1", "type": "query", "params": [], "bindings": ["9c1gS32R8L2GpH0C1oPQMYHGdlBbIVpLI4Vrqk4x"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.994153, "duration": 0.0177, "duration_str": "17.7ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 85.055}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.024161, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 85.055, "width_percent": 14.945}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview@resetTableFiltersForm<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasFilters.php&line=122\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasFilters.php&line=122\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasFilters.php:122-133</a>", "middleware": "web", "duration": "705ms", "peak_memory": "14MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1588407330 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1588407330\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1985676396 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rrcoElSJLKkRUr415s7j3uX2VaiGMUFUwTsBUWZZ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1876 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:3,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;3&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:true,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:3,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;3&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;wNSGdxO3rx3uuZuhmtZW&quot;,&quot;name&quot;:&quot;app.filament.staff.resources.overview-resource.pages.manage-overview&quot;,&quot;path&quot;:&quot;staff\\/overviews&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;29c1898d4e899b41f52c9ac11bf4e82e04ec9fa992bef8b86e3cd36ac736f3ae&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"21 characters\">resetTableFiltersForm</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985676396\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1444374662 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IjU0WFFHaTVUUjNoUUwySjB1WVlvbFE9PSIsInZhbHVlIjoiakNqb29GQjgxSEdwTmdzQXR3ZXJyMHhSSDdZeDlwMTdjNTRla2lWVnBrVUduS01aQzZVM3VoWityR2VZSVZUUS9tOW1sRHVyWFZ2TDd2aHdsNzBYcFVpTWxadURieWJIU2h0RS9nSTUyTTdzanhjR25YQjJYcmhla3R2bTZWY0MiLCJtYWMiOiI1OGQwZDc0OTE0NDU4NGJkMjIzNzFjMDY5ZWVhYTk3NzdmYmMwZTJmODUyOWUwMzNjNDkxYjc2ZjM0MWNlODEzIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IlIwYUdLUTRlekxLblNqY2FxdXh2eWc9PSIsInZhbHVlIjoiNHk0K2RwMWtvZk5aZHhvQkx0YTFhRDVybXA1b214Z0ZWL2F6ZVRkSUo2YnlPai8ybmY2THpxaWMvUjF3b0RqaHFRTDB0dGtsMVUxUVFKc1dDTGJtamFSejlzcUNSSjY5SmRTTmQ3SXUrUGZ2bTkvWXRwb29XR2tESC9WeUV4M1kiLCJtYWMiOiJjNTA0MjJmZmI2MzkxYmUyMzhiNmNjOWI3Y2M2NGMyYjAxZTJjYjM0MWEyZDg1NmM3YjEwNzc5YzA4YWVmN2EzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">https://portal.racoed.test/staff/overviews?tableFilters[overview_filter][school_session_id]=3&amp;tableFilters[overview_filter][semester_id]=1&amp;tableFilters[overview_filter][level_id]=1&amp;tableFilters[overview_filter][department_id]=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2277</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444374662\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-572110497 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rrcoElSJLKkRUr415s7j3uX2VaiGMUFUwTsBUWZZ</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9c1gS32R8L2GpH0C1oPQMYHGdlBbIVpLI4Vrqk4x</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572110497\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1608528217 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 19:20:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1608528217\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-699853750 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rrcoElSJLKkRUr415s7j3uX2VaiGMUFUwTsBUWZZ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://portal.racoed.test/staff/overviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-699853750\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": "500 Internal Server Error"}}