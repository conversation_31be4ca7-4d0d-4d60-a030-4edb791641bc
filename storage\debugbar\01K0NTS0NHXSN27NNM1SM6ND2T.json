{"__meta": {"id": "01K0NTS0NHXSN27NNM1SM6ND2T", "datetime": "2025-07-21 06:29:42", "utime": **********.70631, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": *********1.914486, "end": **********.70633, "duration": 0.7918441295623779, "duration_str": "792ms", "measures": [{"label": "Booting", "start": *********1.914486, "relative_start": 0, "end": **********.316608, "relative_end": **********.316608, "duration": 0.*****************, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.316632, "relative_start": 0.****************, "end": **********.706333, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.319531, "relative_start": 0.****************, "end": **********.31988, "relative_end": **********.31988, "duration": 0.0003490447998046875, "duration_str": "349μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.567733, "relative_start": 0.****************, "end": **********.567733, "relative_end": **********.567733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.590241, "relative_start": 0.****************, "end": **********.590241, "relative_end": **********.590241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.606337, "relative_start": 0.6918511390686035, "end": **********.606337, "relative_end": **********.606337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.619693, "relative_start": 0.705207109451294, "end": **********.619693, "relative_end": **********.619693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.629504, "relative_start": 0.7150180339813232, "end": **********.629504, "relative_end": **********.629504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-portal-access-banner", "start": **********.678451, "relative_start": 0.7639651298522949, "end": **********.678451, "relative_end": **********.678451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.hooks.global-bio-data-banner", "start": **********.689568, "relative_start": 0.7750821113586426, "end": **********.689568, "relative_end": **********.689568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3e7af8a9f04a41a7583e36fe677e9b1b", "start": **********.694153, "relative_start": 0.7796671390533447, "end": **********.694153, "relative_end": **********.694153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.70341, "relative_start": 0.788923978805542, "end": **********.704575, "relative_end": **********.704575, "duration": 0.001165151596069336, "duration_str": "1.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7101608, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 8, "nb_templates": 8, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.567668, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.590119, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.606218, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.619578, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.629379, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament.hooks.global-portal-access-banner", "param_count": null, "params": [], "start": **********.678256, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.phpfilament.hooks.global-portal-access-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=1", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "?"}}, {"name": "filament.hooks.global-bio-data-banner", "param_count": null, "params": [], "start": **********.689488, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-bio-data-banner.blade.phpfilament.hooks.global-bio-data-banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-bio-data-banner.blade.php&line=1", "ajax": false, "filename": "global-bio-data-banner.blade.php", "line": "?"}}, {"name": "__components::3e7af8a9f04a41a7583e36fe677e9b1b", "param_count": null, "params": [], "start": **********.694032, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/3e7af8a9f04a41a7583e36fe677e9b1b.blade.php__components::3e7af8a9f04a41a7583e36fe677e9b1b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F3e7af8a9f04a41a7583e36fe677e9b1b.blade.php&line=1", "ajax": false, "filename": "3e7af8a9f04a41a7583e36fe677e9b1b.blade.php", "line": "?"}}]}, "queries": {"count": 22, "nb_statements": 21, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06393999999999998, "accumulated_duration_str": "63.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.329134, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'iykGtSU3ci4GPISdbEElBQQG01pZvNP9dq0o3OV2' limit 1", "type": "query", "params": [], "bindings": ["iykGtSU3ci4GPISdbEElBQQG01pZvNP9dq0o3OV2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.331218, "duration": 0.04018, "duration_str": "40.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 62.84}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.4191802, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 62.84, "width_percent": 2.393}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.4576352, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 65.233, "width_percent": 1.83}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 20, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 22, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.462018, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 67.063, "width_percent": 1.095}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-07-21' and date(`semester_end`) >= '2025-07-21' limit 1", "type": "query", "params": [], "bindings": [3, "2025-07-21", "2025-07-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.466568, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 68.158, "width_percent": 1.58}, {"sql": "select * from `semesters` where `semesters`.`id` in (2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 23, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/ListRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Resources\\Pages\\ListRecords.php", "line": 81}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/InteractsWithTable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\InteractsWithTable.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/filament/support/src/Components/ComponentManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Components\\ComponentManager.php", "line": 80}, {"index": 26, "namespace": null, "name": "vendor/filament/support/src/Concerns/Configurable.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\Configurable.php", "line": 12}], "start": **********.471053, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 69.737, "width_percent": 1.157}, {"sql": "select * from `school_sessions` where `school_sessions`.`id` = 3 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 150}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.485148, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:150", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=150", "ajax": false, "filename": "OverviewResource.php", "line": "150"}, "connection": "racoed", "explain": null, "start_percent": 70.895, "width_percent": 1.095}, {"sql": "select * from `semesters` where `semesters`.`id` = '1' and `semesters`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 156}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.488714, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:156", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=156", "ajax": false, "filename": "OverviewResource.php", "line": "156"}, "connection": "racoed", "explain": null, "start_percent": 71.989, "width_percent": 1.017}, {"sql": "select * from `levels` where `levels`.`id` = '1' and `levels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.492725, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:162", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=162", "ajax": false, "filename": "OverviewResource.php", "line": "162"}, "connection": "racoed", "explain": null, "start_percent": 73.006, "width_percent": 1.72}, {"sql": "select * from `departments` where `departments`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 168}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/HasIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\HasIndicators.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasFilterIndicators.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasFilterIndicators.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 24}], "start": **********.4966881, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:168", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=168", "ajax": false, "filename": "OverviewResource.php", "line": "168"}, "connection": "racoed", "explain": null, "start_percent": 74.726, "width_percent": 1.048}, {"sql": "select exists(select * from `score_sheets` where (`school_session_id` = 3 and `semester_id` = '1' and `department_id` = '3')) as `exists`", "type": "query", "params": [], "bindings": [3, "1", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 361}, {"index": 13, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 122}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.503758, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:372", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 372}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=372", "ajax": false, "filename": "OverviewResource.php", "line": "372"}, "connection": "racoed", "explain": null, "start_percent": 75.774, "width_percent": 1.298}, {"sql": "select exists(select * from `score_sheets` where (`school_session_id` = 3 and `semester_id` = '1' and `department_id` = '3' and `is_published` = 1)) as `exists`", "type": "query", "params": [], "bindings": [3, "1", "3", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 384}, {"index": 12, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 361}, {"index": 13, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 122}, {"index": 14, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 57}], "start": **********.5096982, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:384", "source": {"index": 11, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=384", "ajax": false, "filename": "OverviewResource.php", "line": "384"}, "connection": "racoed", "explain": null, "start_percent": 77.072, "width_percent": 1.298}, {"sql": "select * from `departments` where `departments`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 129}, {"index": 21, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Filters/Concerns/InteractsWithTableQuery.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\Concerns\\InteractsWithTableQuery.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Filters/SelectFilter.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Filters\\SelectFilter.php", "line": 143}, {"index": 24, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasFilters.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasFilters.php", "line": 153}], "start": **********.515301, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:129", "source": {"index": 20, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=129", "ajax": false, "filename": "OverviewResource.php", "line": "129"}, "connection": "racoed", "explain": null, "start_percent": 78.37, "width_percent": 1.282}, {"sql": "select count(*) as aggregate from `users` where `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = 3 and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '3' or `second_department_id` = '3'))))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 3, "1", "1", "3", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 18, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.519912, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:34", "source": {"index": 15, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=34", "ajax": false, "filename": "CanPaginateRecords.php", "line": "34"}, "connection": "racoed", "explain": null, "start_percent": 79.653, "width_percent": 3.754}, {"sql": "select * from `users` where `role` = 1 and (exists (select * from `registrations` where `users`.`id` = `registrations`.`user_id` and `school_session_id` = 3 and `semester_id` = '1' and `level_id` = '1' and (exists (select * from `programmes` where `registrations`.`programme_id` = `programmes`.`id` and (`first_department_id` = '3' or `second_department_id` = '3'))))) and `users`.`deleted_at` is null order by `last_name` asc limit 10 offset 0", "type": "query", "params": [], "bindings": [1, 3, "1", "1", "3", "3"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 18, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 19, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.5271041, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 16, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 83.406, "width_percent": 3.988}, {"sql": "select * from `registrations` where `registrations`.`user_id` in (10, 11, 12, 13, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, {"index": 22, "namespace": null, "name": "vendor/filament/tables/src/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\HasRecords.php", "line": 111}, {"index": 23, "namespace": null, "name": "vendor/filament/tables/src/Table/Concerns/HasRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Table\\Concerns\\HasRecords.php", "line": 66}, {"index": 24, "namespace": "view", "name": "filament-tables::index", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\/../resources/views/index.blade.php", "line": 66}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.534115, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "CanPaginateRecords.php:37", "source": {"index": 21, "namespace": null, "name": "vendor/filament/tables/src/Concerns/CanPaginateRecords.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\tables\\src\\Concerns\\CanPaginateRecords.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FCanPaginateRecords.php&line=37", "ajax": false, "filename": "CanPaginateRecords.php", "line": "37"}, "connection": "racoed", "explain": null, "start_percent": 87.394, "width_percent": 2.471}, {"sql": "select `name`, `id` from `school_sessions` where `school_sessions`.`deleted_at` is null order by `name` desc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 87}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.5577369, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:87", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 87}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=87", "ajax": false, "filename": "OverviewResource.php", "line": "87"}, "connection": "racoed", "explain": null, "start_percent": 89.865, "width_percent": 3.331}, {"sql": "select `name`, `id` from `semesters` where `semesters`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 97}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.580504, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:97", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=97", "ajax": false, "filename": "OverviewResource.php", "line": "97"}, "connection": "racoed", "explain": null, "start_percent": 93.197, "width_percent": 2.205}, {"sql": "select `name`, `id` from `levels` where `levels`.`deleted_at` is null order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 107}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.5975711, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:107", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 107}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=107", "ajax": false, "filename": "OverviewResource.php", "line": "107"}, "connection": "racoed", "explain": null, "start_percent": 95.402, "width_percent": 1.345}, {"sql": "select `name`, `id` from `departments` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 117}, {"index": 15, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 16, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 655}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 167}], "start": **********.612123, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "OverviewResource.php:117", "source": {"index": 14, "namespace": null, "name": "app/Filament/Staff/Resources/OverviewResource.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Resources\\OverviewResource.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FResources%2FOverviewResource.php&line=117", "ajax": false, "filename": "OverviewResource.php", "line": "117"}, "connection": "racoed", "explain": null, "start_percent": 96.747, "width_percent": 1.204}, {"sql": "select exists(select * from `registrations` where `registrations`.`user_id` = 1 and `registrations`.`user_id` is not null and not exists (select * from `invoices` where `registrations`.`id` = `invoices`.`payable_id` and `invoices`.`payable_type` = 'App\\\\Models\\\\Registration' and `invoice_status` = 3 and `fee_type` = 1 and `invoices`.`deleted_at` is null)) as `exists`", "type": "query", "params": [], "bindings": [1, "App\\Models\\Registration", 3, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 15}, {"index": 16, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.684474, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "filament.hooks.global-portal-access-banner:15", "source": {"index": 14, "namespace": "view", "name": "filament.hooks.global-portal-access-banner", "file": "C:\\Users\\<USER>\\Herd\\racoed\\resources\\views/filament/hooks/global-portal-access-banner.blade.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Ffilament%2Fhooks%2Fglobal-portal-access-banner.blade.php&line=15", "ajax": false, "filename": "global-portal-access-banner.blade.php", "line": "15"}, "connection": "racoed", "explain": null, "start_percent": 97.951, "width_percent": 2.049}]}, "models": {"data": {"App\\Models\\Department": {"value": 18, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}, "App\\Models\\User": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Registration": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FRegistration.php&line=1", "ajax": false, "filename": "Registration.php", "line": "?"}}, "App\\Models\\Semester": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\SchoolSession": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\SemesterSchedule": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemesterSchedule.php&line=1", "ajax": false, "filename": "SemesterSchedule.php", "line": "?"}}, "App\\Models\\Level": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FLevel.php&line=1", "ajax": false, "filename": "Level.php", "line": "?"}}}, "count": 38, "is_counter": true}, "livewire": {"data": {"app.filament.staff.resources.overview-resource.pages.manage-overview #jiRBhUyxkgtRnL20noI0": "array:4 [\n  \"data\" => array:38 [\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => 3\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"3\"\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => true\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => []\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => array:1 [\n      \"overview_filter\" => array:4 [\n        \"school_session_id\" => 3\n        \"semester_id\" => \"1\"\n        \"level_id\" => \"1\"\n        \"department_id\" => \"3\"\n      ]\n    ]\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"app.filament.staff.resources.overview-resource.pages.manage-overview\"\n  \"component\" => \"App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview\"\n  \"id\" => \"jiRBhUyxkgtRnL20noI0\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Staff\\Resources\\OverviewResource\\Pages\\ManageOverview@applyTableFilters<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasFilters.php&line=135\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Ffilament%2Ftables%2Fsrc%2FConcerns%2FHasFilters.php&line=135\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/tables/src/Concerns/HasFilters.php:135-140</a>", "middleware": "web", "duration": "801ms", "peak_memory": "12MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-743620434 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-743620434\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-983694553 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0tMxIa5AtSrPdmZqKxmNgp7jDSoF0BHOpI8BhwX</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"1876 characters\">{&quot;data&quot;:{&quot;isTableReordering&quot;:false,&quot;tableFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:3,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;3&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;tableGrouping&quot;:null,&quot;tableGroupingDirection&quot;:null,&quot;tableSearch&quot;:&quot;&quot;,&quot;tableSortColumn&quot;:null,&quot;tableSortDirection&quot;:null,&quot;activeTab&quot;:null,&quot;mountedActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;defaultAction&quot;:null,&quot;defaultActionArguments&quot;:null,&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedInfolistActionsComponent&quot;:null,&quot;mountedInfolistActionsInfolist&quot;:null,&quot;isTableLoaded&quot;:true,&quot;tableRecordsPerPage&quot;:10,&quot;tableColumnSearches&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;toggledTableColumns&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableActionRecord&quot;:null,&quot;defaultTableAction&quot;:null,&quot;defaultTableActionArguments&quot;:null,&quot;defaultTableActionRecord&quot;:null,&quot;selectedTableRecords&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedTableBulkAction&quot;:null,&quot;mountedTableBulkActionData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;tableDeferredFilters&quot;:[{&quot;overview_filter&quot;:[{&quot;school_session_id&quot;:3,&quot;semester_id&quot;:&quot;1&quot;,&quot;level_id&quot;:&quot;1&quot;,&quot;department_id&quot;:&quot;3&quot;},{&quot;s&quot;:&quot;arr&quot;}]},{&quot;s&quot;:&quot;arr&quot;}],&quot;paginators&quot;:[{&quot;page&quot;:1},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;jiRBhUyxkgtRnL20noI0&quot;,&quot;name&quot;:&quot;app.filament.staff.resources.overview-resource.pages.manage-overview&quot;,&quot;path&quot;:&quot;staff\\/overviews&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;7af6a697c9056d141d4f1b2911249df185cfbc53404350ce10f7f131d3504175&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"17 characters\">applyTableFilters</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983694553\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1003751150 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IklKMUlaeWJMRVprMTBEUS8weG1HL2c9PSIsInZhbHVlIjoid05ZdHhKVmZ3OVorWWNLY3lHenZDeVFPSklOcFVtU2ZRRUhzYXUvTkx1SXBMdWdTK3ZEUjIzMjFwaFNteUI1eDZrNUxROVQ4WUNIVW1KcS82Sm15dTgxQ3g4SnU1b0s0NEZyLzFnMzhWejBxc2xLMzhGTTBwdDJ6WmtxRmxQUnAiLCJtYWMiOiJkZWVmYmRmOTc3Zjc0ZWMzMjFjMWNjOWIzOTNhMWIwNzM4MWU4YTk5NGEyNzJjNDVmYTI4Y2FjZDQxZjdjNjEzIiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6Ik9ZU1dEc3dyK2Mva2p6VnMzSmVKNUE9PSIsInZhbHVlIjoiQ0loNEJvV3EwWWE5M3FWdEUyMTJSaW4rT1ErRTkxR2FFbWJoc3IxZERqZy9rVmNGUlFlM1JWVE1zRmJUcEkyMnhJcGlXVXByTUwvYVF0cTd6VGZTZklmWURlbEZQV1lyaUUrTXNQWEFIYU5ZdzZ4U0VUeC9zYTM3QlE0ZzFnSVciLCJtYWMiOiIzMzBhOTk0OTBlMzU0N2E1ZWM0YzRjODVkYTE1MWQ0NzQ3ZWQ1MWUwNDk3ZTg2MmQzYTA4NzUzOWRlZWNkZDlmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">https://portal.racoed.test/staff/overviews?tableFilters[overview_filter][school_session_id]=3&amp;tableFilters[overview_filter][semester_id]=1&amp;tableFilters[overview_filter][level_id]=1&amp;tableFilters[overview_filter][department_id]=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2273</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003751150\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2102119683 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0tMxIa5AtSrPdmZqKxmNgp7jDSoF0BHOpI8BhwX</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iykGtSU3ci4GPISdbEElBQQG01pZvNP9dq0o3OV2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2102119683\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-114639906 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 06:29:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114639906\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1772780558 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">r0tMxIa5AtSrPdmZqKxmNgp7jDSoF0BHOpI8BhwX</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"259 characters\">https://portal.racoed.test/staff/overviews?tableFilters%5Boverview_filter%5D%5Bschool_session_id%5D=3&amp;tableFilters%5Boverview_filter%5D%5Bsemester_id%5D=1&amp;tableFilters%5Boverview_filter%5D%5Blevel_id%5D=1&amp;tableFilters%5Boverview_filter%5D%5Bdepartment_id%5D=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://portal.racoed.test/staff/overviews</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K0NRCQMSDAABPPH4TRKZVZ2W</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772780558\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}