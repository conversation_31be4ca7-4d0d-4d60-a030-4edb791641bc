<?php

namespace App\Filament\Student\Pages\Auth;

use Filament\Forms\Form;
use Filament\Pages\Auth\EditProfile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use App\Filament\Components\StudentForm;
use App\Filament\Student\Pages\Dashboard;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;

class StudentProfile extends EditProfile
{

    public function getTitle(): string | Htmlable
    {
        return 'Bio-data';
    }

    public static function getSlug(): string
    {
        return 'bio-data';
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $student = Auth::user();
        $data['guardian']['title'] = $student->guardian?->title ?? null;
        $data['guardian']['relationship'] = $student->guardian?->relationship ?? null;
        $data['guardian']['occupation'] = $student->guardian?->occupation ?? null;
        $data['guardian']['phone'] = $student->guardian?->phone ?? null;
        $data['guardian']['first_name'] = $student->guardian?->first_name ?? null;
        $data['guardian']['last_name'] = $student->guardian?->last_name ?? null;
        $data['application']['secondary_school_attended'] = $student->application?->secondary_school_attended ?? null;
        $data['application']['secondary_school_graduation_year'] = $student->application?->secondary_school_graduation_year ?? null;
        $data['application']['jamb_registration_number'] = $student->application?->jamb_registration_number ?? null;
        $data['application']['exam_board'] = $student->application?->exam_board ?? null;
        $data['application']['programme_id'] = $student->application?->programme?->id ?? null;
        $data['application']['school_session_id'] = $student->application?->schoolSession?->id ?? null;
        $data['application']['exam_result'] =  $student->application?->exam_result ?? [];
        $data['activeRegistration']['school_session_id'] = $student->activeRegistration?->schoolSession?->id ?? null;
        $data['activeRegistration']['semester_id'] = $student->activeRegistration?->semester?->id ?? null;
        $data['activeRegistration']['level_id'] = $student->activeRegistration?->level?->id ?? null;
        $data['activeRegistration']['programme_id'] = $student->activeRegistration?->programme?->id ?? null;

        return $data;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema(
                StudentForm::schema(),
            );
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        DB::transaction(function () use ($record, $data) {
            $userData = collect($data)->except(['application', 'registrations', 'guardian', 'activeRegistration'])->toArray();
            $record->update($userData);

            if (isset($data['guardian'])) {
                $record->guardian()->updateOrCreate([], $data['guardian']);
            }

            if (isset($data['application'])) {
                $record->application()->updateOrCreate([], $data['application']);
            }

            if (isset($data['registrations'])) {
                $record->registrations()->updateOrCreate([], $data['registrations']);
            }
        });

        return $record->fresh();
    }

    protected function getSavedNotification(): ?Notification
    {

        return Notification::make()
            ->success()
            ->title('Bio-data Updated')
            ->body('Your bio-data has been saved successfully');
    }

    protected function getRedirectUrl(): ?string
    {
        return Dashboard::getUrl(['panel' => 'student']);
    }
}
