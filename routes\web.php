<?php

use App\Livewire\Home;
use App\Livewire\News;
use App\Livewire\Contact;
use App\Livewire\NewsShow;
use App\Livewire\ResultChecker;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use App\Livewire\NceFullTimeApplication;
use App\Http\Controllers\BioDataController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\RedirectController;
use App\Http\Controllers\EventWebhookController;
use App\Http\Controllers\AdmissionLetterController;
use App\Http\Controllers\ApplicationDataController;
use App\Http\Controllers\AcceptanceLetterController;
use App\Http\Controllers\RegistrationController;

//HOME
Route::get('/', Home::class)->name('home');
Route::get('/result-checker', ResultChecker::class)->name('result-checker');
Route::view('/about', 'pages.about')->name('about');
Route::get('/contact', Contact::class)->name('contact');
Route::get('/news', News::class)->name('news.index');
Route::get('/news/{slug}', NewsShow::class)->name('news.show');
Route::view('/nce-full-time', 'pages.nce-full-time')->name('nce-full-time');
Route::get('/nce-full-time/application', NceFullTimeApplication::class)->name('nce.full-time.application');
Route::view('/thank-you', 'pages.thank-you')->name('thank-you');
Route::view('/teaching-practice', 'pages.teaching-practice')->name('teaching-practice');
// Route::view('/academic-calendar', 'pages.academic-calendar')->name('academic-calendar');

//SCHOOLS
Route::view('/schools/arts', 'pages.schools.arts')->name('schools.arts');
Route::view('/schools/languages', 'pages.schools.languages')->name('schools.languages');
Route::view('/schools/sciences', 'pages.schools.sciences')->name('schools.sciences');
Route::view('/schools/vocational', 'pages.schools.vocational')->name('schools.vocational');
Route::view('/schools/education', 'pages.schools.education')->name('schools.education');
Route::view('/schools/general', 'pages.schools.general')->name('schools.general');

//DEPARTMENTS
Route::view('/departments/economics', 'pages.departments.economics')->name('departments.economics');
Route::view('/departments/islamic', 'pages.departments.islamic')->name('departments.islamic');
Route::view('/departments/political', 'pages.departments.political')->name('departments.political');
Route::view('/departments/yoruba', 'pages.departments.yoruba')->name('departments.yoruba');
Route::view('/departments/social', 'pages.departments.social')->name('departments.social');
Route::view('/departments/mathematics', 'pages.departments.mathematics')->name('departments.mathematics');
Route::view('/departments/physics', 'pages.departments.physics')->name('departments.physics');
Route::view('/departments/biology', 'pages.departments.biology')->name('departments.biology');
Route::view('/departments/chemistry', 'pages.departments.chemistry')->name('departments.chemistry');
Route::view('/departments/computer', 'pages.departments.computer')->name('departments.computer');
Route::view('/departments/business', 'pages.departments.business')->name('departments.business');
Route::view('/departments/arabic', 'pages.departments.arabic')->name('departments.arabic');
Route::view('/departments/english', 'pages.departments.english')->name('departments.english');
Route::view('/departments/french', 'pages.departments.french')->name('departments.french');

//PAYSTACK
Route::post('events/webhook', [EventWebhookController::class, 'handleEvent'])
    ->name('events.webhook');

//PORTAL
Route::domain(config('custom.portal_url'))->group(function () {
    //Guest
    Route::get('/', function () {
        if (Auth::check()) {

            return app(RedirectController::class)->handleRoleRedirect();
        }

        return redirect()->route('filament.auth.auth.login');
    })->name('login');

    //Auth
    Route::middleware(['auth', 'signed'])->group(function () {
        Route::get('/invoice/{invoice}/print', [InvoiceController::class, 'print'])->name('invoice.print');
        Route::get('/invoice/{invoice}/download', [InvoiceController::class, 'download'])->name('invoice.download');

        Route::get('/application-data/print/{student}', [ApplicationDataController::class, 'print'])->name('application-data.print');
        Route::get('/application-data/download/{student}', [ApplicationDataController::class, 'download'])->name('application-data.download');

        Route::get('/admission-letter/print/{student}', [AdmissionLetterController::class, 'print'])->name('admission-letter.print');
        Route::get('/admission-letter/download/{student}', [AdmissionLetterController::class, 'download'])->name('admission-letter.download');

        Route::get('/acceptance-letter/print/{student}', [AcceptanceLetterController::class, 'print'])->name('acceptance-letter.print');
        Route::get('/acceptance-letter/download/{student}', [AcceptanceLetterController::class, 'download'])->name('acceptance-letter.download');

        Route::get('/bio-data/print/{student}', [BioDataController::class, 'print'])->name('bio-data.print');
        Route::get('/bio-data/download/{student}', [BioDataController::class, 'download'])->name('bio-data.download');

        Route::get('/registration/print/{registration}', [RegistrationController::class, 'print'])->name('registration.print');
        Route::get('/registration/download/{registration}', [RegistrationController::class, 'download'])->name('registration.download');
    });
});
