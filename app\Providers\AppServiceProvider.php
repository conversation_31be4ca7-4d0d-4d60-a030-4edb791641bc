<?php

namespace App\Providers;

use App\Models\User;
use Illuminate\View\View;
use Carbon\CarbonImmutable;
use Filament\Support\Assets\Js;
use Filament\Support\Colors\Color;
use Illuminate\Support\Facades\DB;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;
use Opcodes\LogViewer\Facades\LogViewer;
use Illuminate\Support\Facades\Validator;
use App\Http\Responses\MultiLoginResponse;
use Filament\Support\Facades\FilamentView;
use Filament\Support\Facades\FilamentAsset;
use Filament\Support\Facades\FilamentColor;
use App\Filament\Staff\Clusters\Scores\Resources\ScoreResource;
use Guava\FilamentKnowledgeBase\Filament\Panels\KnowledgeBasePanel;
use Filament\Http\Responses\Auth\Contracts\LoginResponse as LoginResponseContract;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {

        $this->app->singleton(
            LoginResponseContract::class,
            MultiLoginResponse::class
        );

        KnowledgeBasePanel::configureUsing(
            fn(KnowledgeBasePanel $panel) => $panel
                ->viteTheme('resources/css/filament/custom/theme.css')
                ->brandName('Help Center')
                ->favicon(asset('images/racoed-favicon.png'))
        );

        FilamentView::registerRenderHook(
            PanelsRenderHook::AUTH_LOGIN_FORM_BEFORE,
            fn(): View => view('filament.hooks.school-name'),
        );

        FilamentView::registerRenderHook(
            PanelsRenderHook::USER_MENU_BEFORE,
            fn(): View => view('filament.hooks.welcome-user'),
        );

        FilamentView::registerRenderHook(
            PanelsRenderHook::TOPBAR_START,
            fn(): View => view('filament.hooks.school-details'),
        );

        FilamentView::registerRenderHook(
            PanelsRenderHook::RESOURCE_PAGES_LIST_RECORDS_TABLE_BEFORE,
            fn(): View => view('filament.hooks.score-sheet-banner'),
            scopes: ScoreResource::class,
        );

        FilamentView::registerRenderHook(
            PanelsRenderHook::PAGE_START,
            fn(): View => view('filament.hooks.global-portal-access-banner')
        );

        // FilamentView::registerRenderHook(
        //     PanelsRenderHook::PAGE_START,
        //     fn(): View => view('filament.hooks.global-bio-data-banner')
        // );
    }


    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->configureUrl();
        $this->configureModels();
        $this->configureCommands();
        $this->configureDates();

        FilamentAsset::register([
            Js::make('paystack-library', 'https://js.paystack.co/v2/inline.js'),
            Js::make('paystack', __DIR__ . '/../../resources/js/paystack.js'),
        ]);

        //injecting google analytics in filament
        FilamentView::registerRenderHook(
            PanelsRenderHook::HEAD_START,
            fn(): View => view('filament.hooks.analytics-tag'),
        );

        LogViewer::auth(function ($request) {
            return $request->user()
                && in_array($request->user()->email, [
                    config('custom.admin_email'),
                ]);
        });

        Gate::define('viewPulse', function (User $user) {
            return $user->email === config('custom.admin_email');
        });

        FilamentColor::register([
            'danger' => Color::Red,
            'gray' => Color::Zinc,
            'info' => Color::Blue,
            'success' => Color::Green,
            'warning' => Color::Amber,
            'primary' => [
                50 => '251, 233, 231',    // Lightest
                100 => '245, 208, 204',   // Very light red
                200 => '236, 173, 167',   // Light red
                300 => '226, 134, 123',   // Medium light red
                400 => '216, 94, 79',     // Medium red
                500 => '150, 40, 27',     // Base red (#96281B)
                600 => '120, 32, 22',     // Slightly darker
                700 => '90, 24, 17',      // Darker red
                800 => '60, 16, 11',      // Very dark red
                900 => '45, 12, 8',       // Extra dark red
                950 => '30, 8, 5',        // Darkest red
            ],
            'white' => [
                50 => '255, 255, 255',   // Pure white (#FFFFFF)
                100 => '252, 252, 252',  // Nearly white
                200 => '250, 250, 250',  // Very slight gray
                300 => '247, 247, 247',  // Light gray
                400 => '244, 244, 244',  // Light-medium gray
                500 => '240, 240, 240',  // Medium gray
                600 => '237, 237, 237',  // Medium-dark gray
                700 => '233, 233, 233',  // Dark gray
                800 => '229, 229, 229',  // Darker gray
                900 => '224, 224, 224',  // Very dark gray
                950 => '220, 220, 220',  // Darkest gray
            ],
        ]);
    }

    /**
     * Configure the application's URL.
     *
     */
    private function configureUrl(): void
    {
        URL::forceScheme('https');
    }

    /**
     * Configure the application's models.
     *
     */
    private function configureModels(): void
    {
        Model::unguard();
        Model::preventLazyLoading(app()->environment('local'));
    }

    /**
     * Configure the application's commands.
     *
     */
    private function configureCommands(): void
    {
        DB::prohibitDestructiveCommands(app()->environment('production'));
    }

    /**
     * Configure the application's dates.
     *
     */
    private function configureDates(): void
    {
        Date::use(CarbonImmutable::class);
    }
}
