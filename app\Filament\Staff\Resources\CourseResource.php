<?php

namespace App\Filament\Staff\Resources;


use Filament\Tables;
use App\Models\Course;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Enums\CourseStatus;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Filters\SelectFilter;
use App\Filament\Staff\Resources\CourseResource\Pages;

class CourseResource extends Resource
{
    protected static ?string $model = Course::class;
    protected static ?int $navigationSort = 7;
    protected static ?string $navigationGroup = 'Academic';
    protected static ?string $navigationBadgeTooltip = 'Total number of courses';

    public static function canAccess(): bool
    {
        return all_staff_access();
    }

    public static function canEdit($record): bool
    {
        return ict_access();
    }

    public static function canDelete($record): bool
    {
        return ict_access();
    }

    public static function canCreate(): bool
    {
        return ict_access();
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getEloquentQuery()->count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('code')
                    ->required()
                    ->maxLength(15)
                    ->placeholder('CSC 111')
                    ->unique(ignoreRecord: true),
                TextInput::make('title')
                    ->required()
                    ->maxLength(40)
                    ->placeholder('Introduction to Programming'),
                TextInput::make('credit')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(5)
                    ->placeholder(1)
                    ->datalist([0, 1, 2, 3]),
                Select::make('course_status')
                    ->required()
                    ->label('Course Status')
                    ->options(CourseStatus::class)
                    ->placeholder('Select a course status'),
                Select::make('department_id')
                    ->required()
                    ->label('Department')
                    ->native(false)
                    ->relationship('department', 'name')
                    ->placeholder('Select a department'),
                Select::make('semester_id')
                    ->required()
                    ->label('Semester')
                    ->native(false)
                    ->relationship('semester', 'name')
                    ->placeholder('Select a semester'),
                Select::make('level_id')
                    ->required()
                    ->label('Level')
                    ->native(false)
                    ->relationship('level', 'name')
                    ->placeholder('Select a level'),
            ])->columns(4);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->deferLoading()
            ->deferFilters()
            ->persistFiltersInSession()
            ->paginated([10, 20, 30, 40, 50])
            ->striped()
            ->recordAction(null)
            ->defaultSort('code')
            ->emptyStateHeading('No Courses Yet')
            ->emptyStateDescription('Once you create your first course, it will appear here.')
            ->description('The list of courses created for efficient academic management.')
            ->searchPlaceholder('Search (course code)')

            ->columns([
                TextColumn::make('#')
                    ->rowIndex(),
                TextColumn::make('code')
                    ->searchable(),
                TextColumn::make('title')
                    ->words(3)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();

                        $wordCount = str_word_count($state);

                        if ($wordCount <= 3) {
                            return null;
                        }

                        return $state;
                    }),
                TextColumn::make('credit'),
                TextColumn::make('course_status')
                    ->label('Status')
                    ->badge(),
                TextColumn::make('department.name'),
                TextColumn::make('semester.name'),
                TextColumn::make('level.name'),
            ])
            ->filters([
                SelectFilter::make('department_id')
                    ->relationship('department', 'name')
                    ->label('Department'),
                SelectFilter::make('semester_id')
                    ->relationship('semester', 'name')
                    ->label('Semester'),
                SelectFilter::make('level_id')
                    ->relationship('level', 'name')
                    ->label('Level'),
            ], layout: FiltersLayout::AboveContent)
            ->filtersApplyAction(
                fn(Action $action) => $action->label('View courses'),
            )

            ->actions([
                ActionGroup::make([
                    ViewAction::make(),
                    EditAction::make()
                        ->successNotification(function () {
                            return Notification::make()
                                ->success()
                                ->title('Course Updated')
                                ->body('The course has been saved successfully.');
                        }),
                    DeleteAction::make()
                        ->modalHeading('Delete Course?')
                        ->modalDescription('Are you sure you want to delete this course?')
                        ->modalSubmitActionLabel('Delete')
                        ->action(function ($record, DeleteAction $action) {
                            $relations = ['registrations'];
                            $hasRelations = [];

                            foreach ($relations as $relation) {
                                if (method_exists($record, $relation) && $record->{$relation}()->exists()) {
                                    $hasRelations[] = $relation;
                                }
                            }

                            if (!empty($hasRelations)) {
                                $message = 'You can’t delete this course because it has related: ' . implode(', ', $hasRelations) . '.';

                                Notification::make()
                                    ->danger()
                                    ->title('Deletion Failed')
                                    ->body($message)
                                    ->send();

                                $action->halt();
                                return;
                            }

                            $record->delete();

                            Notification::make()
                                ->success()
                                ->title('Course Deleted')
                                ->body('The course has been deleted successfully.')
                                ->send();
                        })

                ])
                    ->tooltip('Course actions'),
            ])

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCourses::route('/'),
        ];
    }
}
