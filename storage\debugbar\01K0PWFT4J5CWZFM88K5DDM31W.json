{"__meta": {"id": "01K0PWFT4J5CWZFM88K5DDM31W", "datetime": "2025-07-21 16:18:52", "utime": **********.691098, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753114731.757208, "end": **********.691122, "duration": 0.9339139461517334, "duration_str": "934ms", "measures": [{"label": "Booting", "start": 1753114731.757208, "relative_start": 0, "end": **********.403178, "relative_end": **********.403178, "duration": 0.****************, "duration_str": "646ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.403201, "relative_start": 0.****************, "end": **********.691126, "relative_end": 4.0531158447265625e-06, "duration": 0.*****************, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.415052, "relative_start": 0.***************, "end": **********.41626, "relative_end": **********.41626, "duration": 0.0012080669403076172, "duration_str": "1.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.690441, "relative_start": 0.****************, "end": **********.690735, "relative_end": **********.690735, "duration": 0.0002942085266113281, "duration_str": "294μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 7096792, "peak_usage_str": "7MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 31, "nb_statements": 30, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07352999999999998, "accumulated_duration_str": "73.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.428926, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = '9c1gS32R8L2GpH0C1oPQMYHGdlBbIVpLI4Vrqk4x' limit 1", "type": "query", "params": [], "bindings": ["9c1gS32R8L2GpH0C1oPQMYHGdlBbIVpLI4Vrqk4x"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.42989, "duration": 0.0272, "duration_str": "27.2ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 36.992}, {"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.465569, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "racoed", "explain": null, "start_percent": 36.992, "width_percent": 5.617}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 31}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.4947531, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 42.608, "width_percent": 2.339}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 32}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.50509, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 44.948, "width_percent": 1.863}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_start` asc", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 63}, {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 19, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.511383, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:63", "source": {"index": 15, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=63", "ajax": false, "filename": "AcademicCalendar.php", "line": "63"}, "connection": "racoed", "explain": null, "start_percent": 46.811, "width_percent": 1.945}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.520463, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 48.756, "width_percent": 2.856}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-07-21' and date(`semester_end`) >= '2025-07-21' limit 1", "type": "query", "params": [], "bindings": [3, "2025-07-21", "2025-07-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.526788, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 51.612, "width_percent": 1.21}, {"sql": "select * from `semesters` where `semesters`.`id` in (2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.537556, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 52.822, "width_percent": 2.339}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 37}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.5435379, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 55.161, "width_percent": 1.02}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-07-21' and date(`semester_end`) >= '2025-07-21' limit 1", "type": "query", "params": [], "bindings": [3, "2025-07-21", "2025-07-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.5480049, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 56.181, "width_percent": 1.265}, {"sql": "select * from `semesters` where `semesters`.`id` in (2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 37}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.555817, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 57.446, "width_percent": 0.979}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 37}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.5600219, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 58.425, "width_percent": 1.265}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 3 and `semester_id` = 2 limit 1", "type": "query", "params": [], "bindings": [3, 2], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 91}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 37}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.564984, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:91", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=91", "ajax": false, "filename": "AcademicCalendar.php", "line": "91"}, "connection": "racoed", "explain": null, "start_percent": 59.69, "width_percent": 1.686}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.5729752, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 61.376, "width_percent": 1.129}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-07-21' and date(`semester_end`) >= '2025-07-21' limit 1", "type": "query", "params": [], "bindings": [3, "2025-07-21", "2025-07-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.577547, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 62.505, "width_percent": 1.686}, {"sql": "select * from `semesters` where `semesters`.`id` in (2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.583511, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 64.191, "width_percent": 1.197}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.5895758, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 65.388, "width_percent": 1.074}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and `semester_start` > '2025-07-21 16:18:52' order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [3, "2025-07-21 16:18:52"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 128}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.594026, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:128", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 128}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=128", "ajax": false, "filename": "AcademicCalendar.php", "line": "128"}, "connection": "racoed", "explain": null, "start_percent": 66.463, "width_percent": 2.38}, {"sql": "select `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_end` desc limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 212}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 135}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}], "start": **********.599634, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:212", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 212}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=212", "ajax": false, "filename": "AcademicCalendar.php", "line": "212"}, "connection": "racoed", "explain": null, "start_percent": 68.843, "width_percent": 3.182}, {"sql": "select * from `school_sessions` where exists (select * from `semester_schedules` where `school_sessions`.`id` = `semester_schedules`.`school_session_id` and `semester_start` > '2025-08-30 23:59:59') and `school_sessions`.`deleted_at` is null order by `id` asc limit 1", "type": "query", "params": [], "bindings": ["2025-08-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 142}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.608548, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:142", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 142}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=142", "ajax": false, "filename": "AcademicCalendar.php", "line": "142"}, "connection": "racoed", "explain": null, "start_percent": 72.025, "width_percent": 5.984}, {"sql": "select * from `semester_schedules` where `school_session_id` = 4 order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 148}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.618186, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:148", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=148", "ajax": false, "filename": "AcademicCalendar.php", "line": "148"}, "connection": "racoed", "explain": null, "start_percent": 78.009, "width_percent": 4.882}, {"sql": "select * from `semesters` where `semesters`.`id` in (1) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 148}, {"index": 22, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 25, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.625633, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:148", "source": {"index": 21, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=148", "ajax": false, "filename": "AcademicCalendar.php", "line": "148"}, "connection": "racoed", "explain": null, "start_percent": 82.891, "width_percent": 1.02}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 19, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 21, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 22, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.629679, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 83.911, "width_percent": 1.863}, {"sql": "select * from `semester_schedules` where `school_session_id` = 3 and date(`semester_start`) <= '2025-07-21' and date(`semester_end`) >= '2025-07-21' limit 1", "type": "query", "params": [], "bindings": [3, "2025-07-21", "2025-07-21"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.6358402, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 85.775, "width_percent": 3.25}, {"sql": "select * from `semesters` where `semesters`.`id` in (2) and `semesters`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, {"index": 23, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 24, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 25, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 26, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.641733, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "helpers.php:28", "source": {"index": 21, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=28", "ajax": false, "filename": "helpers.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 89.025, "width_percent": 0.979}, {"sql": "select * from `school_sessions` where `is_active` = 1 and `school_sessions`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 21, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.645819, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "helpers.php:13", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Helpers\\helpers.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FHelpers%2Fhelpers.php&line=13", "ajax": false, "filename": "helpers.php", "line": "13"}, "connection": "racoed", "explain": null, "start_percent": 90.004, "width_percent": 0.898}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 3 and `semester_start` > '2025-07-21 16:18:52' order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [3, "2025-07-21 16:18:52"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 180}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.656442, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:180", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=180", "ajax": false, "filename": "AcademicCalendar.php", "line": "180"}, "connection": "racoed", "explain": null, "start_percent": 90.902, "width_percent": 1.183}, {"sql": "select `semester_end` from `semester_schedules` where `school_session_id` = 3 order by `semester_end` desc limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 212}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 187}, {"index": 18, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}], "start": **********.6608012, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:212", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 212}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=212", "ajax": false, "filename": "AcademicCalendar.php", "line": "212"}, "connection": "racoed", "explain": null, "start_percent": 92.085, "width_percent": 1.129}, {"sql": "select * from `school_sessions` where exists (select * from `semester_schedules` where `school_sessions`.`id` = `semester_schedules`.`school_session_id` and `semester_start` > '2025-08-30 23:59:59') and `school_sessions`.`deleted_at` is null order by `id` asc limit 1", "type": "query", "params": [], "bindings": ["2025-08-30 23:59:59"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 194}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.6661582, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:194", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 194}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=194", "ajax": false, "filename": "AcademicCalendar.php", "line": "194"}, "connection": "racoed", "explain": null, "start_percent": 93.214, "width_percent": 4.923}, {"sql": "select `semester_start`, `semester_end` from `semester_schedules` where `school_session_id` = 4 order by `semester_start` asc limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/StatsOverviewWidget.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\StatsOverviewWidget.php", "line": 29}, {"index": 20, "namespace": "view", "name": "filament-widgets::stats-overview-widget", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\filament\\widgets\\src\\/../resources/views/stats-overview-widget.blade.php", "line": 2}], "start": **********.6736, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "AcademicCalendar.php:199", "source": {"index": 16, "namespace": null, "name": "app/Filament/Staff/Widgets/AcademicCalendar.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Filament\\Staff\\Widgets\\AcademicCalendar.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FFilament%2FStaff%2FWidgets%2FAcademicCalendar.php&line=199", "ajax": false, "filename": "AcademicCalendar.php", "line": "199"}, "connection": "racoed", "explain": null, "start_percent": 98.137, "width_percent": 1.863}]}, "models": {"data": {"App\\Models\\SchoolSession": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSchoolSession.php&line=1", "ajax": false, "filename": "SchoolSession.php", "line": "?"}}, "App\\Models\\SemesterSchedule": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemesterSchedule.php&line=1", "ajax": false, "filename": "SemesterSchedule.php", "line": "?"}}, "App\\Models\\Semester": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FSemester.php&line=1", "ajax": false, "filename": "Semester.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 28, "is_counter": true}, "livewire": {"data": {"app.filament.staff.widgets.academic-calendar #V0xYrwKlBV2cOmS26J2A": "array:4 [\n  \"data\" => []\n  \"name\" => \"app.filament.staff.widgets.academic-calendar\"\n  \"component\" => \"App\\Filament\\Staff\\Widgets\\AcademicCalendar\"\n  \"id\" => \"V0xYrwKlBV2cOmS26J2A\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "851ms", "peak_memory": "18MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1146363783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1146363783\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1232186505 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rrcoElSJLKkRUr415s7j3uX2VaiGMUFUwTsBUWZZ</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"325 characters\">{&quot;data&quot;:[],&quot;memo&quot;:{&quot;id&quot;:&quot;V0xYrwKlBV2cOmS26J2A&quot;,&quot;name&quot;:&quot;app.filament.staff.widgets.academic-calendar&quot;,&quot;path&quot;:&quot;staff\\/dashboard&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;b248f4985413c6a98333e4332f13728988872934f48a3c1b6e9d890f0ca0920f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IkFLMVpDWXRVOHBHTXM4aGY4ZFIxIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiI4YjNkOWUyM2ZmMjk1NDUxYmMwOTQ1M2M2ZDk4ZTU2MTliNmZiNDRiNDJhMDk4N2M2YzMzMDcyMGMyODEyMzY3In0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232186505\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-17240375 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"712 characters\">XSRF-TOKEN=eyJpdiI6IlJ3YkVpdDcrWEVOVW9ibzRDR3hUbVE9PSIsInZhbHVlIjoiSkdpaGZCbU1qL0RzbXkrQnE3WG1ZS1hmTjAxYWRlWUlndzNBMlJvR29tb09qbXJLS3hVU0N1cm1uMlk1SmY0VEl2R1YvQjF4Mi94V01RNVZFT3lrSTM3THo2eFRxQXVJS0RBM0xBeFk0U05JZHp4OGNQdGM3UUs5RFJ0MjZXSVQiLCJtYWMiOiIxZDUxYzNhMTdmYzY0YzAxZDJmNDg5NmQ3MmIyMzE3OTBiMzYwZWZmMDZlNmJiZjZlNzg3MDlmZjliNWVjN2M4IiwidGFnIjoiIn0%3D; racoed_session=eyJpdiI6IjhDVXU4aE5sRFlWdFZYWHRjVnFjK3c9PSIsInZhbHVlIjoiM2FuQm40SVBNNWlIZytkYUEzTWVnbWZSUzNMd1ZqMGZNK0Fzak5xQUJyQjcxaTJlWlJ2YTNKYnhWVEp2RnYvSkp3dUR4RDgxTk9qclRKN3pvWUNzVWdFWmVFOUNLMFdITHRsTXozWVRhOVJsaEFoV08zQkQ5L1pFTDFqaUY3SWwiLCJtYWMiOiIyZThhNWJkNGE3NGE3MzEzZGNkNjAwNDllMjM1NzUyYTEzMTczMjQ5MzA5YmNiMDIzMGEwODY5NmZmZGE2MTcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=1, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://portal.racoed.test/staff/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://portal.racoed.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">769</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">portal.racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17240375\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-52300296 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rrcoElSJLKkRUr415s7j3uX2VaiGMUFUwTsBUWZZ</span>\"\n  \"<span class=sf-dump-key>racoed_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9c1gS32R8L2GpH0C1oPQMYHGdlBbIVpLI4Vrqk4x</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52300296\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-906837776 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 16:18:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906837776\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-731932494 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rrcoElSJLKkRUr415s7j3uX2VaiGMUFUwTsBUWZZ</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://portal.racoed.test/staff/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$6JlgUMnp3xgDIXcBSW9tBOdkiMl8cFGpcZp3mRsCx4OsmfDQVYYNG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-731932494\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://portal.racoed.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}