{"__meta": {"id": "01K0MH60FGF75GXTBZE8D6CEZ9", "datetime": "2025-07-20 18:22:48", "utime": **********.305796, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753035765.417957, "end": **********.305844, "duration": 2.8878870010375977, "duration_str": "2.89s", "measures": [{"label": "Booting", "start": 1753035765.417957, "relative_start": 0, "end": **********.903914, "relative_end": **********.903914, "duration": 1.****************, "duration_str": "1.49s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.903963, "relative_start": 1.****************, "end": **********.305849, "relative_end": 5.0067901611328125e-06, "duration": 1.***************, "duration_str": "1.4s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.960148, "relative_start": 1.****************, "end": **********.979542, "relative_end": **********.979542, "duration": 0.****************, "duration_str": "19.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.pages.home", "start": **********.119449, "relative_start": 1.****************, "end": **********.119449, "relative_end": **********.119449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3048ed5cd6535b4240154657b3fe863b", "start": **********.242508, "relative_start": 1.****************, "end": **********.242508, "relative_end": **********.242508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5d1a04211e80c13a56f8c60a9372775a", "start": **********.292606, "relative_start": 1.8746490478515625, "end": **********.292606, "relative_end": **********.292606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::21d2e5f1b7448b2c9bb26fe3e30c4381", "start": **********.339841, "relative_start": 1.9218838214874268, "end": **********.339841, "relative_end": **********.339841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::374a5a544950f4867e550f9b1e8471cf", "start": **********.400294, "relative_start": 1.9823369979858398, "end": **********.400294, "relative_end": **********.400294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.694336, "relative_start": 2.276378870010376, "end": **********.694336, "relative_end": **********.694336, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.app", "start": **********.800242, "relative_start": 2.3822848796844482, "end": **********.800242, "relative_end": **********.800242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.831439, "relative_start": 2.4134819507598877, "end": **********.831439, "relative_end": **********.831439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.832196, "relative_start": 2.414238929748535, "end": **********.832196, "relative_end": **********.832196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.832609, "relative_start": 2.414651870727539, "end": **********.832609, "relative_end": **********.832609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.833063, "relative_start": 2.4151058197021484, "end": **********.833063, "relative_end": **********.833063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.833594, "relative_start": 2.4156370162963867, "end": **********.833594, "relative_end": **********.833594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.834142, "relative_start": 2.416184902191162, "end": **********.834142, "relative_end": **********.834142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.834728, "relative_start": 2.4167709350585938, "end": **********.834728, "relative_end": **********.834728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.83509, "relative_start": 2.41713285446167, "end": **********.83509, "relative_end": **********.83509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: seo::tags.tag", "start": **********.835379, "relative_start": 2.417421817779541, "end": **********.835379, "relative_end": **********.835379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.navigation", "start": **********.852424, "relative_start": 2.43446683883667, "end": **********.852424, "relative_end": **********.852424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.application-logo", "start": **********.855958, "relative_start": 2.4380009174346924, "end": **********.855958, "relative_end": **********.855958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.857919, "relative_start": 2.4399619102478027, "end": **********.857919, "relative_end": **********.857919, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.859044, "relative_start": 2.441087007522583, "end": **********.859044, "relative_end": **********.859044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f0d18f5d809abc9eeb963cb6d8001c2c", "start": **********.874841, "relative_start": 2.4568839073181152, "end": **********.874841, "relative_end": **********.874841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.917809, "relative_start": 2.499851942062378, "end": **********.917809, "relative_end": **********.917809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::317fc950001efedaea7f85707674c947", "start": **********.936225, "relative_start": 2.518267869949341, "end": **********.936225, "relative_end": **********.936225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f0d18f5d809abc9eeb963cb6d8001c2c", "start": **********.999082, "relative_start": 2.581125020980835, "end": **********.999082, "relative_end": **********.999082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.999423, "relative_start": 2.58146595954895, "end": **********.999423, "relative_end": **********.999423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.001393, "relative_start": 2.5834360122680664, "end": **********.001393, "relative_end": **********.001393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.002442, "relative_start": 2.584484815597534, "end": **********.002442, "relative_end": **********.002442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.00309, "relative_start": 2.5851328372955322, "end": **********.00309, "relative_end": **********.00309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.012379, "relative_start": 2.594421863555908, "end": **********.012379, "relative_end": **********.012379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.013314, "relative_start": 2.5953569412231445, "end": **********.013314, "relative_end": **********.013314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f0d18f5d809abc9eeb963cb6d8001c2c", "start": **********.013918, "relative_start": 2.595960855484009, "end": **********.013918, "relative_end": **********.013918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.014379, "relative_start": 2.596421957015991, "end": **********.014379, "relative_end": **********.014379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::317fc950001efedaea7f85707674c947", "start": **********.015349, "relative_start": 2.5973918437957764, "end": **********.015349, "relative_end": **********.015349, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f0d18f5d809abc9eeb963cb6d8001c2c", "start": **********.027814, "relative_start": 2.6098568439483643, "end": **********.027814, "relative_end": **********.027814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.028267, "relative_start": 2.6103098392486572, "end": **********.028267, "relative_end": **********.028267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.030307, "relative_start": 2.6123499870300293, "end": **********.030307, "relative_end": **********.030307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.030887, "relative_start": 2.6129298210144043, "end": **********.030887, "relative_end": **********.030887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.responsive-nav-link", "start": **********.0314, "relative_start": 2.613442897796631, "end": **********.0314, "relative_end": **********.0314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.layouts.footer", "start": **********.032324, "relative_start": 2.6143670082092285, "end": **********.032324, "relative_end": **********.032324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.033724, "relative_start": 2.615767002105713, "end": **********.033724, "relative_end": **********.033724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.034621, "relative_start": 2.616663932800293, "end": **********.034621, "relative_end": **********.034621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.035507, "relative_start": 2.6175498962402344, "end": **********.035507, "relative_end": **********.035507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.036152, "relative_start": 2.618194818496704, "end": **********.036152, "relative_end": **********.036152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.038317, "relative_start": 2.6203598976135254, "end": **********.038317, "relative_end": **********.038317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.039538, "relative_start": 2.6215808391571045, "end": **********.039538, "relative_end": **********.039538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.040391, "relative_start": 2.62243390083313, "end": **********.040391, "relative_end": **********.040391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.04163, "relative_start": 2.6236729621887207, "end": **********.04163, "relative_end": **********.04163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::125a69c7cec8a1bc3f9fb56c41a00c37", "start": **********.062412, "relative_start": 2.6444549560546875, "end": **********.062412, "relative_end": **********.062412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.114804, "relative_start": 2.6968469619750977, "end": **********.114804, "relative_end": **********.114804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3ec66060cbcceacefc12b263754b3162", "start": **********.116648, "relative_start": 2.698690891265869, "end": **********.116648, "relative_end": **********.116648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.162981, "relative_start": 2.7450239658355713, "end": **********.162981, "relative_end": **********.162981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f6455a7b1a177bd637ab95a06439425a", "start": **********.176271, "relative_start": 2.7583138942718506, "end": **********.176271, "relative_end": **********.176271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.215856, "relative_start": 2.797899007797241, "end": **********.215856, "relative_end": **********.215856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4ea308770148b61e48e1ede5bd7338b9", "start": **********.234613, "relative_start": 2.8166558742523193, "end": **********.234613, "relative_end": **********.234613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.nav-link", "start": **********.278347, "relative_start": 2.8603899478912354, "end": **********.278347, "relative_end": **********.278347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.social-icon", "start": **********.279066, "relative_start": 2.8611090183258057, "end": **********.279066, "relative_end": **********.279066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.300422, "relative_start": 2.88246488571167, "end": **********.300768, "relative_end": **********.300768, "duration": 0.0003459453582763672, "duration_str": "346μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 8135144, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "racoed.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 56, "nb_templates": 56, "templates": [{"name": "1x livewire.pages.home", "param_count": null, "params": [], "start": **********.119359, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/livewire/pages/home.blade.phplivewire.pages.home", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Flivewire%2Fpages%2Fhome.blade.php&line=1", "ajax": false, "filename": "home.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.pages.home"}, {"name": "1x __components::3048ed5cd6535b4240154657b3fe863b", "param_count": null, "params": [], "start": **********.242458, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/3048ed5cd6535b4240154657b3fe863b.blade.php__components::3048ed5cd6535b4240154657b3fe863b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F3048ed5cd6535b4240154657b3fe863b.blade.php&line=1", "ajax": false, "filename": "3048ed5cd6535b4240154657b3fe863b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3048ed5cd6535b4240154657b3fe863b"}, {"name": "1x __components::5d1a04211e80c13a56f8c60a9372775a", "param_count": null, "params": [], "start": **********.292526, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/5d1a04211e80c13a56f8c60a9372775a.blade.php__components::5d1a04211e80c13a56f8c60a9372775a", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F5d1a04211e80c13a56f8c60a9372775a.blade.php&line=1", "ajax": false, "filename": "5d1a04211e80c13a56f8c60a9372775a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5d1a04211e80c13a56f8c60a9372775a"}, {"name": "1x __components::21d2e5f1b7448b2c9bb26fe3e30c4381", "param_count": null, "params": [], "start": **********.339717, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/21d2e5f1b7448b2c9bb26fe3e30c4381.blade.php__components::21d2e5f1b7448b2c9bb26fe3e30c4381", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F21d2e5f1b7448b2c9bb26fe3e30c4381.blade.php&line=1", "ajax": false, "filename": "21d2e5f1b7448b2c9bb26fe3e30c4381.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::21d2e5f1b7448b2c9bb26fe3e30c4381"}, {"name": "1x __components::374a5a544950f4867e550f9b1e8471cf", "param_count": null, "params": [], "start": **********.40019, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/374a5a544950f4867e550f9b1e8471cf.blade.php__components::374a5a544950f4867e550f9b1e8471cf", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F374a5a544950f4867e550f9b1e8471cf.blade.php&line=1", "ajax": false, "filename": "374a5a544950f4867e550f9b1e8471cf.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::374a5a544950f4867e550f9b1e8471cf"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.694247, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x components.layouts.app", "param_count": null, "params": [], "start": **********.800159, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/components/layouts/app.blade.phpcomponents.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.app"}, {"name": "9x seo::tags.tag", "param_count": null, "params": [], "start": **********.831351, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\vendor\\ralphjsmit\\laravel-seo\\src\\/../resources/views/tags/tag.blade.phpseo::tags.tag", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fralphjsmit%2Flaravel-seo%2Fresources%2Fviews%2Ftags%2Ftag.blade.php&line=1", "ajax": false, "filename": "tag.blade.php", "line": "?"}, "render_count": 9, "name_original": "seo::tags.tag"}, {"name": "1x components.layouts.navigation", "param_count": null, "params": [], "start": **********.852335, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/components/layouts/navigation.blade.phpcomponents.layouts.navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.navigation"}, {"name": "1x components.application-logo", "param_count": null, "params": [], "start": **********.855874, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/components/application-logo.blade.phpcomponents.application-logo", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Fcomponents%2Fapplication-logo.blade.php&line=1", "ajax": false, "filename": "application-logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.application-logo"}, {"name": "19x components.nav-link", "param_count": null, "params": [], "start": **********.857841, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/components/nav-link.blade.phpcomponents.nav-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Fcomponents%2Fnav-link.blade.php&line=1", "ajax": false, "filename": "nav-link.blade.php", "line": "?"}, "render_count": 19, "name_original": "components.nav-link"}, {"name": "4x __components::f0d18f5d809abc9eeb963cb6d8001c2c", "param_count": null, "params": [], "start": **********.874758, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/f0d18f5d809abc9eeb963cb6d8001c2c.blade.php__components::f0d18f5d809abc9eeb963cb6d8001c2c", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Ff0d18f5d809abc9eeb963cb6d8001c2c.blade.php&line=1", "ajax": false, "filename": "f0d18f5d809abc9eeb963cb6d8001c2c.blade.php", "line": "?"}, "render_count": 4, "name_original": "__components::f0d18f5d809abc9eeb963cb6d8001c2c"}, {"name": "2x __components::317fc950001efedaea7f85707674c947", "param_count": null, "params": [], "start": **********.936056, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/317fc950001efedaea7f85707674c947.blade.php__components::317fc950001efedaea7f85707674c947", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F317fc950001efedaea7f85707674c947.blade.php&line=1", "ajax": false, "filename": "317fc950001efedaea7f85707674c947.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::317fc950001efedaea7f85707674c947"}, {"name": "7x components.responsive-nav-link", "param_count": null, "params": [], "start": **********.012295, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/components/responsive-nav-link.blade.phpcomponents.responsive-nav-link", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Fcomponents%2Fresponsive-nav-link.blade.php&line=1", "ajax": false, "filename": "responsive-nav-link.blade.php", "line": "?"}, "render_count": 7, "name_original": "components.responsive-nav-link"}, {"name": "1x components.layouts.footer", "param_count": null, "params": [], "start": **********.032205, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/components/layouts/footer.blade.phpcomponents.layouts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.layouts.footer"}, {"name": "1x __components::125a69c7cec8a1bc3f9fb56c41a00c37", "param_count": null, "params": [], "start": **********.062325, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/125a69c7cec8a1bc3f9fb56c41a00c37.blade.php__components::125a69c7cec8a1bc3f9fb56c41a00c37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F125a69c7cec8a1bc3f9fb56c41a00c37.blade.php&line=1", "ajax": false, "filename": "125a69c7cec8a1bc3f9fb56c41a00c37.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::125a69c7cec8a1bc3f9fb56c41a00c37"}, {"name": "1x __components::3ec66060cbcceacefc12b263754b3162", "param_count": null, "params": [], "start": **********.116567, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/3ec66060cbcceacefc12b263754b3162.blade.php__components::3ec66060cbcceacefc12b263754b3162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F3ec66060cbcceacefc12b263754b3162.blade.php&line=1", "ajax": false, "filename": "3ec66060cbcceacefc12b263754b3162.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3ec66060cbcceacefc12b263754b3162"}, {"name": "1x __components::f6455a7b1a177bd637ab95a06439425a", "param_count": null, "params": [], "start": **********.176204, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/f6455a7b1a177bd637ab95a06439425a.blade.php__components::f6455a7b1a177bd637ab95a06439425a", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2Ff6455a7b1a177bd637ab95a06439425a.blade.php&line=1", "ajax": false, "filename": "f6455a7b1a177bd637ab95a06439425a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f6455a7b1a177bd637ab95a06439425a"}, {"name": "1x __components::4ea308770148b61e48e1ede5bd7338b9", "param_count": null, "params": [], "start": **********.234488, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\storage\\framework\\views/4ea308770148b61e48e1ede5bd7338b9.blade.php__components::4ea308770148b61e48e1ede5bd7338b9", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fstorage%2Fframework%2Fviews%2F4ea308770148b61e48e1ede5bd7338b9.blade.php&line=1", "ajax": false, "filename": "4ea308770148b61e48e1ede5bd7338b9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4ea308770148b61e48e1ede5bd7338b9"}, {"name": "1x components.social-icon", "param_count": null, "params": [], "start": **********.278985, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Herd\\racoed\\resources\\views/components/social-icon.blade.phpcomponents.social-icon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fresources%2Fviews%2Fcomponents%2Fsocial-icon.blade.php&line=1", "ajax": false, "filename": "social-icon.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.social-icon"}]}, "queries": {"count": 7, "nb_statements": 6, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08775000000000001, "accumulated_duration_str": "87.75ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 95}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 158}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 56}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}], "start": **********.011, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "SessionManager.php:108", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/SessionManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\SessionManager.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FSessionManager.php&line=108", "ajax": false, "filename": "SessionManager.php", "line": "108"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `sessions` where `id` = 'DS6J6ijzXrch4Dxpn1VCN4P5tPccZVvAiNY0oXPT' limit 1", "type": "query", "params": [], "bindings": ["DS6J6ijzXrch4Dxpn1VCN4P5tPccZVvAiNY0oXPT"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.026238, "duration": 0.05024, "duration_str": "50.24ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "racoed", "explain": null, "start_percent": 0, "width_percent": 57.254}, {"sql": "select * from `news` where `is_published` = 1 order by `created_at` desc limit 3", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Livewire/Home.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Livewire\\Home.php", "line": 12}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 96}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.0919309, "duration": 0.009869999999999999, "duration_str": "9.87ms", "memory": 0, "memory_str": null, "filename": "Home.php:12", "source": {"index": 15, "namespace": null, "name": "app/Livewire/Home.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Livewire\\Home.php", "line": 12}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FLivewire%2FHome.php&line=12", "ajax": false, "filename": "Home.php", "line": "12"}, "connection": "racoed", "explain": null, "start_percent": 57.254, "width_percent": 11.248}, {"sql": "select * from `cache` where `key` in ('settings_defaults')", "type": "query", "params": [], "bindings": ["settings_defaults"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.146079, "duration": 0.004889999999999999, "duration_str": "4.89ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "racoed", "explain": null, "start_percent": 68.501, "width_percent": 5.573}, {"sql": "delete from `cache` where `key` in ('settings_defaults', 'illuminate:cache:flexible:created:settings_defaults') and `expiration` <= **********", "type": "query", "params": [], "bindings": ["settings_defaults", "illuminate:cache:flexible:created:settings_defaults", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 409}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 144}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 119}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 422}], "start": **********.1573849, "duration": 0.01177, "duration_str": "11.77ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:409", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 409}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=409", "ajax": false, "filename": "DatabaseStore.php", "line": "409"}, "connection": "racoed", "explain": null, "start_percent": 74.074, "width_percent": 13.413}, {"sql": "insert into `cache` (`expiration`, `key`, `value`) values (1753039367, 'settings_defaults', 'a:3:{s:7:\\\"college\\\";a:9:{s:4:\\\"name\\\";s:35:\\\"Raphat College of Education Obaagun\\\";s:5:\\\"motto\\\";s:28:\\\"Where Students are Achievers\\\";s:7:\\\"address\\\";s:56:\\\"Km 2, Iree Expressway, Igbota Area, Obaagun, Osun State.\\\";s:5:\\\"phone\\\";s:11:\\\"08034567890\\\";s:5:\\\"email\\\";s:18:\\\"<EMAIL>\\\";s:9:\\\"ict_phone\\\";s:11:\\\"08034567890\\\";s:9:\\\"ict_email\\\";s:17:\\\"<EMAIL>\\\";s:5:\\\"stamp\\\";s:9:\\\"stamp.png\\\";s:14:\\\"registrar_sign\\\";s:18:\\\"registrar-sign.png\\\";}s:9:\\\"admission\\\";a:3:{s:19:\\\"screening_max_score\\\";i:100;s:22:\\\"screening_cut_off_mark\\\";i:50;s:12:\\\"fee_schedule\\\";N;}s:6:\\\"portal\\\";a:1:{s:10:\\\"portal_fee\\\";i:300000;}}') on duplicate key update `expiration` = values(`expiration`), `key` = values(`key`), `value` = values(`value`)", "type": "query", "params": [], "bindings": [1753039367, "settings_defaults", "a:3:{s:7:\"college\";a:9:{s:4:\"name\";s:35:\"Raphat College of Education Obaagun\";s:5:\"motto\";s:28:\"Where Students are Achievers\";s:7:\"address\";s:56:\"Km 2, Iree Expressway, Igbota Area, Obaagun, Osun State.\";s:5:\"phone\";s:11:\"08034567890\";s:5:\"email\";s:18:\"<EMAIL>\";s:9:\"ict_phone\";s:11:\"08034567890\";s:9:\"ict_email\";s:17:\"<EMAIL>\";s:5:\"stamp\";s:9:\"stamp.png\";s:14:\"registrar_sign\";s:18:\"registrar-sign.png\";}s:9:\"admission\";a:3:{s:19:\"screening_max_score\";i:100;s:22:\"screening_cut_off_mark\";i:50;s:12:\"fee_schedule\";N;}s:6:\"portal\";a:1:{s:10:\"portal_fee\";i:300000;}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 241}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 433}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 474}], "start": **********.1774518, "duration": 0.00709, "duration_str": "7.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "racoed", "explain": null, "start_percent": 87.487, "width_percent": 8.08}, {"sql": "select `name`, `payload` from `settings` where `group` = 'college'", "type": "query", "params": [], "bindings": ["college"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, {"index": 16, "namespace": null, "name": "app/Settings/MultiTenantSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\app\\Settings\\MultiTenantSettingsRepository.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 89}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsMapper.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsMapper.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\Settings.php", "line": 277}], "start": **********.192795, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingsRepository.php:28", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-settings/src/SettingsRepositories/DatabaseSettingsRepository.php", "file": "C:\\Users\\<USER>\\Herd\\racoed\\vendor\\spatie\\laravel-settings\\src\\SettingsRepositories\\DatabaseSettingsRepository.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FSettingsRepositories%2FDatabaseSettingsRepository.php&line=28", "ajax": false, "filename": "DatabaseSettingsRepository.php", "line": "28"}, "connection": "racoed", "explain": null, "start_percent": 95.567, "width_percent": 4.433}]}, "models": {"data": {"Spatie\\LaravelSettings\\Models\\SettingsProperty": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fvendor%2Fspatie%2Flaravel-settings%2Fsrc%2FModels%2FSettingsProperty.php&line=1", "ajax": false, "filename": "SettingsProperty.php", "line": "?"}}, "App\\Models\\News": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FModels%2FNews.php&line=1", "ajax": false, "filename": "News.php", "line": "?"}}}, "count": 12, "is_counter": true}, "livewire": {"data": {"home #hPYtIpsSUfzKarAOtrFg": "array:4 [\n  \"data\" => []\n  \"name\" => \"home\"\n  \"component\" => \"App\\Livewire\\Home\"\n  \"id\" => \"hPYtIpsSUfzKarAOtrFg\"\n]", "notifications #vxhOwPDoE226G87df5aY": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3408\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"notifications\"\n  \"component\" => \"Filament\\Notifications\\Livewire\\Notifications\"\n  \"id\" => \"vxhOwPDoE226G87df5aY\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://racoed.test", "action_name": "home", "controller_action": "App\\Livewire\\Home", "uri": "GET /", "controller": "App\\Livewire\\Home@render<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FLivewire%2FHome.php&line=10\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FUSER%2FHerd%2Fracoed%2Fapp%2FLivewire%2FHome.php&line=10\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Livewire/Home.php:10-15</a>", "middleware": "web", "duration": "2.84s", "peak_memory": "12MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1963685812 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1963685812\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1993717863 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1993717863\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-259420507 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">racoed.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259420507\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-427574913 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-427574913\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1695077499 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 20 Jul 2025 18:22:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1695077499\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-211904810 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oiTFcXFZn8DZIVqXyxaqeUEDWODc0pN5Lwkl4LDa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211904810\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://racoed.test", "action_name": "home", "controller_action": "App\\Livewire\\Home"}, "badge": null}}