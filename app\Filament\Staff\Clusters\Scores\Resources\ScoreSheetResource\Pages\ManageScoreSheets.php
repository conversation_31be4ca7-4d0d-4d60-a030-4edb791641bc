<?php

namespace App\Filament\Staff\Clusters\Scores\Resources\ScoreSheetResource\Pages;

use Filament\Actions\CreateAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use App\Filament\Staff\Clusters\Scores\Resources\ScoreSheetResource;

class ManageScoreSheets extends ManageRecords
// renamed
{
    protected static string $resource = ScoreSheetResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->successNotification(function () {
                    return Notification::make()
                        ->success()
                        ->title('Score-sheet Created')
                        ->body('The score-sheet has been created successfully');
                }),
        ];
    }
}
