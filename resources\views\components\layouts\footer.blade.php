@php
    use App\Settings\CollegeSettings;

    $collegeSettings = app(CollegeSettings::class);
@endphp

<footer class="px-4 py-8 sm:px-6 lg:px-8 bg-[#F3F4F6] border-t border-gray-300">

    <div class="max-w-7xl mx-auto">
       {{--  Footer Grid --}}
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8">
            {{--  Quick Links --}}
            <div>
                <h3 class="text-lg font-bold text-gray-400 mb-4">Quick Links</h3>
                <ul class="space-y-2">
                    <li><x-nav-link :href="route('about')" :active="request()->routeIs('about')" wire:navigate>About Us</x-nav-link></li>
                    <li><x-nav-link :href="route('news.index')" :active="request()->routeIs('news.index')" wire:navigate>News</x-nav-link></li>
                    {{-- <li><x-nav-link :href="route('academic-calendar')" :active="request()->routeIs('academic-calendar')" wire:navigate>Academic Calendar</x-nav-link></li> --}}
                    <li><x-nav-link :href="route('nce-full-time')" :active="request()->routeIs('nce-full-time')" wire:navigate>NCE - Full Time</x-nav-link></li>                                                          
                    <li><x-nav-link :href="route('contact')" :active="request()->routeIs('contact')" wire:navigate>Contact Us</x-nav-link></li>           
                </ul>
            </div>

            {{--  Schools --}}
            <div>
                <h3 class="text-lg font-bold text-gray-400 mb-4">Schools</h3>
                <ul class="space-y-2">
                    <li><x-nav-link :href="route('schools.arts')" :active="request()->routeIs('schools.arts')" wire:navigate>Arts</x-nav-link></li>
                    <li><x-nav-link :href="route('schools.languages')" :active="request()->routeIs('schools.languages')" wire:navigate>Languages</x-nav-link></li>
                    <li><x-nav-link :href="route('schools.sciences')" :active="request()->routeIs('schools.sciences')" wire:navigate>Sciences</x-nav-link></li>
                    <li><x-nav-link :href="route('schools.vocational')" :active="request()->routeIs('schools.vocational')" wire:navigate>Vocational & Technical Ed</x-nav-link></li>
                   
                </ul>
            </div>

            {{--  School Hours --}}
            <div>
                <h3 class="text-lg font-bold text-gray-400 mb-4">School Hours</h3>
                <x-nav-link>
                    <div class="flex items-center gap-2">
                        <x-heroicon-o-clock class="w-5 h-5 flex-shrink-0" />
                        <div>
                            <p class="font-medium">Monday - Friday</p>
                            <p>8:00 AM - 4:00 PM</p>
                        </div>
                    </div>
                </x-nav-link>
            </div>

            {{-- Contact Info --}}
            <div>
                <h3 class="text-lg font-bold text-gray-400 mb-4">Contact Us</h3>
                <div class="space-y-4">                   
                    <div class="flex items-center gap-3">
                        <x-nav-link href="tel:{{ $collegeSettings->getFormattedPhone() }}" class="flex items-center gap-2">
                            <x-heroicon-o-phone class="w-5 h-5 flex-shrink-0" />
                            <span>{{ $collegeSettings->getFormattedPhone() }}</span>
                        </x-nav-link>
                    </div>
                    <div class="flex items-center gap-3">
                        <x-nav-link href="mailto:{{ $collegeSettings->email }}" class="flex items-center gap-2">
                            <x-heroicon-o-envelope class="w-5 h-5 flex-shrink-0" />
                            {{ $collegeSettings->email }}
                        </x-nav-link>
                    </div>
                    <div class="flex items-start gap-3">
                        <x-nav-link class="flex items-start gap-2">
                            <x-heroicon-o-map-pin class="w-5 h-5 flex-shrink-0" />
                            <span>{{ $collegeSettings->address }}</span>
                        </x-nav-link>
                    </div>
                </div>
            </div>

            {{-- Social Links --}}
            <div>
                <h3 class="text-lg font-bold text-gray-400 mb-4">Connect With Us</h3>
                <div class="flex items-center gap-4">
                    <a href="https://web.facebook.com/raphatcoed" target="_blank" class="text-gray-600 hover:text-blue-500 p-2">
                        <x-social-icon icon="facebook" class="w-6 h-6" />
                    </a>
                </div>
            </div>

        </div>

        {{-- Bottom Copyright --}}
        <div class="mt-8 pt-8 border-t border-gray-300 text-gray-500 text-center text-sm">
            <p>
                © {{ date('Y') }} {{ $collegeSettings->name }}. All rights reserved.
                <span>Powered by <a href="https://www.cportal.ng" target="_blank" rel="noopener noreferrer" class="underline hover:text-black">Cportal</a></span>
            </p>
        </div>

    </div>
</footer>