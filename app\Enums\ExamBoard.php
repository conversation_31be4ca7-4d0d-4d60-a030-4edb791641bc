<?php

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum ExamBoard: string implements HasLabel
{
    case WAEC = 'waec';
    case NECO = 'neco';
    case NABTEB = 'nabteb';

    /**
     * Get the label for the enum
     */
    public function getLabel(): string
    {
        return match ($this) {
            self::WAEC => 'WAEC',
            self::NECO => 'NECO',
            self::NABTEB => 'NABTEB',
        };
    }
}
